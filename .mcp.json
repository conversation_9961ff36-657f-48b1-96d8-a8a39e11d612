{"mcpServers": {"github.com/zcaceres/fetch-mcp": {"autoApprove": ["fetch_markdown", "fetch_html", "fetch_json"], "disabled": false, "timeout": 60, "command": "node", "args": ["/mnt/c/Users/<USER>/OneDrive/Documents/Cline/MCP/fetch-mcp/dist/index.js"], "transportType": "stdio"}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem@latest", "."]}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github@latest"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "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", "OPENAI_API_KEY": "********************************************************************************************************************************************************************"}}, "Figma": {"url": "http://127.0.0.1:3845/sse"}}}