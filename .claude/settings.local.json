{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:supabase.com)", "Ba<PERSON>(flutter:*)", "Bash(npx:*)", "Bash(ls:*)", "Bash(node:*)", "Bash(npm:*)", "<PERSON><PERSON>(cat:*)", "mcp__dart__list_tasks", "mcp__dart__get_task", "Bash(grep:*)", "mcp__dart__update_task", "mcp__dart__add_task_comment", "<PERSON><PERSON>(dart:*)", "Bash(supabase functions:*)", "Bash(rm:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(cp:*)", "Bash(export:*)", "<PERSON><PERSON>(chmod:*)", "mcp__supabase__execute_sql", "mcp__supabase__list_tables", "Bash(./deploy_ai_configuration.bat)", "mcp__supabase__list_projects", "mcp__supabase__deploy_edge_function", "mcp__dart__create_task", "mcp__dart__get_config", "Bash(git add:*)", "mcp__supabase__apply_migration", "mcp__supabase__list_edge_functions", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(python3:*)", "Bash(kill:*)", "mcp__filesystem__list_directory", "Bash(git checkout:*)", "Bash(wmctrl:*)", "<PERSON><PERSON>(curl:*)", "mcp__github__search_code", "mcp__github__get_file_contents", "mcp__github__search_repositories", "<PERSON><PERSON>(git clone:*)", "Bash(git remote remove:*)", "Bash(git remote add:*)", "Bash(git config:*)", "Bash(git branch:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(apt list:*)", "Bash(adb.exe devices:*)", "Bash(pwsh:*)", "Bash(powershell.exe:*)", "Bash(./copy_v2_assets.sh:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(touch:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(sed:*)", "Bash(ln:*)", "Bash(./fix_web_assets.sh:*)", "Bash(git rm:*)", "Bash(git reset:*)", "<PERSON><PERSON>(diff:*)", "<PERSON><PERSON>(jq:*)", "Bash(sudo rm:*)", "Bash(./clean_build.sh:*)", "<PERSON><PERSON>(git clean:*)", "Bash(git pull:*)", "mcp__taskmaster-ai__initialize_project", "mcp__taskmaster-ai__parse_prd", "mcp__taskmaster-ai__get_tasks", "mcp__taskmaster-ai__remove_task", "Bash(git fetch:*)", "mcp__filesystem__read_file", "mcp__filesystem__read_multiple_files", "mcp__filesystem__write_file", "mcp__filesystem__directory_tree", "WebFetch(domain:raw.githubusercontent.com)", "mcp__taskmaster-ai__set_task_status", "mcp__taskmaster-ai__expand_task", "mcp__taskmaster-ai__update_subtask", "mcp__supabase__get_project_url", "mcp__taskmaster-ai__update_task", "mcp__taskmaster-ai__get_task", "mcp__supabase__list_migrations", "mcp__taskmaster-ai__add_task", "mcp__taskmaster-ai__next_task", "Bash(./run_web.sh:*)", "Bash(ss:*)", "Bash(for file in bn.json es.json id.json ku.json ph.json ru.json so.json tr.json ur.json)", "Bash(do sed -i 's/\"\"appName\"\": \"\"[^\"\"]*\"\"/\"\"appName\"\": \"\"Quranic Insights\"\"/' \"$file\")", "Bash(done)", "Bash(git ls-tree:*)", "Bash(./fix_imports.sh)", "<PERSON><PERSON>(python:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase", "dart", "filesystem", "github", "task-master-ai"]}