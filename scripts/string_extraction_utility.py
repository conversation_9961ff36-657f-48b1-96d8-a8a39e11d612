#!/usr/bin/env python3
"""
String Extraction Utility for Quranic Insights App

This utility helps identify hardcoded strings in Dart files that should be localized.
It extracts text strings and generates translation keys for use with <PERSON>lutter's localization system.

Usage:
    python3 string_extraction_utility.py [directory_path]

Features:
- Scans Dart files for hardcoded strings
- Identifies potential localization candidates
- Generates suggested translation keys
- Excludes common non-localizable strings (imports, comments, etc.)
- Supports various string formats (single quotes, double quotes)
- Generates reports in multiple formats
"""

import os
import re
import json
import argparse
import sys
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

@dataclass
class StringMatch:
    """Represents a found string that could be localized"""
    file_path: str
    line_number: int
    original_string: str
    suggested_key: str
    context: str
    confidence: float  # 0.0 to 1.0, how likely this should be localized

class StringExtractor:
    """Main class for extracting localizable strings from Dart files"""
    
    def __init__(self):
        # Patterns to match different types of strings
        self.string_patterns = [
            # Single quoted strings
            r"'([^'\\]|\\.)*'",
            # Double quoted strings  
            r'"([^"\\\\]|\\\\.)*"',
            # Raw strings
            r"r'([^']*)'",
            r'r"([^"]*)"',
        ]
        
        # Strings to exclude from localization (imports, technical terms, etc.)
        self.exclusion_patterns = [
            r'^package:',  # Package imports
            r'^dart:',     # Dart core imports
            r'^\./',       # Relative imports
            r'^http',      # URLs
            r'^https',     # URLs
            r'^\w+\.\w+$', # File extensions or technical identifiers
            r'^\d+$',      # Pure numbers
            r'^[A-Z_]+$',  # Constants (all caps)
            r'^[a-z_]+$',  # Variable names (all lowercase with underscores)
            r'^\w+\(\)$',  # Function calls
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',  # Email addresses
        ]
        
        # Common technical terms that usually don't need localization
        self.technical_terms = {
            'true', 'false', 'null', 'void', 'int', 'double', 'String', 'bool',
            'Widget', 'BuildContext', 'Key', 'super', 'this', 'return',
            'const', 'final', 'var', 'class', 'extends', 'implements',
            'async', 'await', 'Future', 'Stream', 'List', 'Map', 'Set',
            'JSON', 'API', 'HTTP', 'URL', 'URI', 'UUID', 'ID', 'UI', 'UX',
            'iOS', 'Android', 'Web', 'macOS', 'Windows', 'Linux',
            'Flutter', 'Dart', 'Firebase', 'Supabase', 'SQLite',
        }
        
        # Keywords that indicate the context might need localization
        self.ui_indicators = {
            'Text(', 'title:', 'subtitle:', 'label:', 'hint:', 'error:',
            'AppBar(', 'SnackBar(', 'AlertDialog(', 'showDialog(',
            'content:', 'actions:', 'body:', 'message:', 'description:',
        }
        
    def extract_from_file(self, file_path: str) -> List[StringMatch]:
        """Extract potential localizable strings from a single Dart file"""
        matches = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
                
            for line_num, line in enumerate(lines, 1):
                # Skip comments and imports
                if self._is_excluded_line(line):
                    continue
                    
                # Find all string literals in the line
                for pattern in self.string_patterns:
                    for match in re.finditer(pattern, line):
                        string_literal = match.group(0)
                        # Remove quotes and unescape
                        inner_string = self._extract_inner_string(string_literal)
                        
                        if self._should_localize(inner_string, line):
                            suggested_key = self._generate_key(inner_string)
                            confidence = self._calculate_confidence(inner_string, line)
                            
                            matches.append(StringMatch(
                                file_path=file_path,
                                line_number=line_num,
                                original_string=string_literal,
                                suggested_key=suggested_key,
                                context=line.strip(),
                                confidence=confidence
                            ))
                            
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
            
        return matches
    
    def extract_from_directory(self, directory: str, recursive: bool = True) -> List[StringMatch]:
        """Extract strings from all Dart files in a directory"""
        all_matches = []
        
        if recursive:
            dart_files = list(Path(directory).rglob("*.dart"))
        else:
            dart_files = list(Path(directory).glob("*.dart"))
            
        print(f"Found {len(dart_files)} Dart files to analyze...")
        
        for file_path in dart_files:
            # Skip generated files and test files for now
            if self._should_skip_file(str(file_path)):
                continue
                
            matches = self.extract_from_file(str(file_path))
            all_matches.extend(matches)
            
        return all_matches
    
    def _is_excluded_line(self, line: str) -> bool:
        """Check if a line should be excluded from analysis"""
        stripped = line.strip()
        
        # Skip empty lines and comments
        if not stripped or stripped.startswith('//') or stripped.startswith('/*'):
            return True
            
        # Skip import statements
        if stripped.startswith('import ') or stripped.startswith('export '):
            return True
            
        # Skip lines that are only imports/exports
        if 'import ' in stripped and stripped.endswith(';'):
            return True
            
        return False
    
    def _extract_inner_string(self, string_literal: str) -> str:
        """Extract the actual string content from a quoted string literal"""
        # Remove outer quotes
        if string_literal.startswith(("'", '"')):
            inner = string_literal[1:-1]
        elif string_literal.startswith(("r'", 'r"')):
            inner = string_literal[2:-1]
        else:
            inner = string_literal
            
        # Basic unescape for common escape sequences
        inner = inner.replace('\\"', '"').replace("\\'", "'")
        inner = inner.replace('\\n', '\n').replace('\\t', '\t')
        
        return inner
    
    def _should_localize(self, text: str, context: str) -> bool:
        """Determine if a string should be localized"""
        # Skip very short strings (likely technical)
        if len(text.strip()) < 2:
            return False
            
        # Skip strings that match exclusion patterns
        for pattern in self.exclusion_patterns:
            if re.match(pattern, text.strip()):
                return False
                
        # Skip technical terms
        if text.strip() in self.technical_terms:
            return False
            
        # Skip strings that are likely variable names or technical identifiers
        if text.strip().replace('_', '').isalnum() and text.strip().islower():
            return False
            
        # Skip strings that look like CSS selectors or technical formats
        if text.startswith(('.', '#', '@', '$')) or '()' in text:
            return False
            
        # Higher priority for strings in UI contexts
        return True
    
    def _generate_key(self, text: str) -> str:
        """Generate a suggested localization key from text"""
        # Clean the text
        clean_text = text.strip().lower()
        
        # Remove punctuation and replace spaces/special chars with underscores
        clean_text = re.sub(r'[^\w\s]', '', clean_text)
        clean_text = re.sub(r'\s+', '_', clean_text)
        clean_text = re.sub(r'_+', '_', clean_text)
        clean_text = clean_text.strip('_')
        
        # Limit length and ensure it's a valid identifier
        if len(clean_text) > 50:
            words = clean_text.split('_')
            clean_text = '_'.join(words[:5])  # Take first 5 words
            
        # Ensure it starts with a letter
        if clean_text and not clean_text[0].isalpha():
            clean_text = 'text_' + clean_text
            
        return clean_text if clean_text else 'unknown_text'
    
    def _calculate_confidence(self, text: str, context: str) -> float:
        """Calculate confidence that this string should be localized"""
        confidence = 0.5  # Base confidence
        
        # Increase confidence for UI-related contexts
        for indicator in self.ui_indicators:
            if indicator in context:
                confidence += 0.2
                break
                
        # Increase confidence for user-facing text patterns
        if any(char.isalpha() for char in text):  # Contains letters
            confidence += 0.1
            
        # Increase confidence for sentences (contains spaces and mixed case)
        if ' ' in text and not text.isupper() and not text.islower():
            confidence += 0.2
            
        # Decrease confidence for very technical looking strings
        if text.count('.') > 1 or text.count('/') > 0:
            confidence -= 0.2
            
        # Decrease confidence for very short strings
        if len(text.strip()) < 4:
            confidence -= 0.1
            
        return max(0.0, min(1.0, confidence))
    
    def _should_skip_file(self, file_path: str) -> bool:
        """Check if a file should be skipped during analysis"""
        # Skip generated files
        if '.g.dart' in file_path or '.freezed.dart' in file_path:
            return True
            
        # Skip test files for now (they might have their own localization needs)
        if '/test/' in file_path or file_path.endswith('_test.dart'):
            return True
            
        # Skip build directories
        if '/build/' in file_path or '/.dart_tool/' in file_path:
            return True
            
        return False

class ReportGenerator:
    """Generate various types of reports from extracted strings"""
    
    def generate_summary_report(self, matches: List[StringMatch]) -> str:
        """Generate a text summary report"""
        if not matches:
            return "No localizable strings found."
            
        # Group by file
        by_file = {}
        for match in matches:
            if match.file_path not in by_file:
                by_file[match.file_path] = []
            by_file[match.file_path].append(match)
            
        # Sort matches by confidence
        high_confidence = [m for m in matches if m.confidence >= 0.7]
        medium_confidence = [m for m in matches if 0.4 <= m.confidence < 0.7]
        low_confidence = [m for m in matches if m.confidence < 0.4]
        
        report = []
        report.append("=" * 80)
        report.append("STRING EXTRACTION UTILITY REPORT")
        report.append("=" * 80)
        report.append(f"Total files analyzed: {len(by_file)}")
        report.append(f"Total strings found: {len(matches)}")
        report.append(f"High confidence (≥0.7): {len(high_confidence)}")
        report.append(f"Medium confidence (0.4-0.7): {len(medium_confidence)}")
        report.append(f"Low confidence (<0.4): {len(low_confidence)}")
        report.append("")
        
        # High confidence matches first
        if high_confidence:
            report.append("HIGH CONFIDENCE MATCHES (Should definitely be localized)")
            report.append("-" * 60)
            for match in sorted(high_confidence, key=lambda m: m.file_path):
                report.append(f"File: {match.file_path}:{match.line_number}")
                report.append(f"String: {match.original_string}")
                report.append(f"Suggested key: {match.suggested_key}")
                report.append(f"Context: {match.context}")
                report.append(f"Confidence: {match.confidence:.2f}")
                report.append("")
                
        return "\n".join(report)
    
    def generate_json_report(self, matches: List[StringMatch]) -> str:
        """Generate a JSON report for programmatic processing"""
        data = {
            "summary": {
                "total_matches": len(matches),
                "high_confidence": len([m for m in matches if m.confidence >= 0.7]),
                "medium_confidence": len([m for m in matches if 0.4 <= m.confidence < 0.7]),
                "low_confidence": len([m for m in matches if m.confidence < 0.4]),
            },
            "matches": [
                {
                    "file_path": match.file_path,
                    "line_number": match.line_number,
                    "original_string": match.original_string,
                    "suggested_key": match.suggested_key,
                    "context": match.context,
                    "confidence": match.confidence,
                }
                for match in matches
            ]
        }
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def generate_translation_template(self, matches: List[StringMatch]) -> str:
        """Generate a template for translation files"""
        # Only include high confidence matches
        high_confidence = [m for m in matches if m.confidence >= 0.7]
        
        # Remove duplicates based on suggested key
        seen_keys = set()
        unique_matches = []
        for match in high_confidence:
            if match.suggested_key not in seen_keys:
                seen_keys.add(match.suggested_key)
                unique_matches.append(match)
                
        template = {}
        for match in unique_matches:
            # Extract clean text for translation
            clean_text = match.original_string.strip("'\"")
            template[match.suggested_key] = clean_text
            
        return json.dumps(template, indent=2, ensure_ascii=False)

def main():
    parser = argparse.ArgumentParser(
        description="Extract localizable strings from Dart files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 string_extraction_utility.py lib/
  python3 string_extraction_utility.py lib/features/auth/
  python3 string_extraction_utility.py . --output-dir reports/
        """
    )
    
    parser.add_argument(
        'directory',
        nargs='?',
        default='lib/',
        help='Directory to scan for Dart files (default: lib/)'
    )
    
    parser.add_argument(
        '--output-dir',
        default='scripts/extraction_reports/',
        help='Output directory for reports (default: scripts/extraction_reports/)'
    )
    
    parser.add_argument(
        '--min-confidence',
        type=float,
        default=0.4,
        help='Minimum confidence threshold for inclusion in reports (default: 0.4)'
    )
    
    parser.add_argument(
        '--format',
        choices=['text', 'json', 'both'],
        default='both',
        help='Output format (default: both)'
    )
    
    args = parser.parse_args()
    
    # Validate directory
    if not os.path.isdir(args.directory):
        print(f"Error: Directory '{args.directory}' does not exist.")
        sys.exit(1)
        
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("🔍 Starting string extraction...")
    print(f"📂 Scanning directory: {args.directory}")
    print(f"📊 Minimum confidence: {args.min_confidence}")
    
    # Extract strings
    extractor = StringExtractor()
    matches = extractor.extract_from_directory(args.directory)
    
    # Filter by confidence
    filtered_matches = [m for m in matches if m.confidence >= args.min_confidence]
    
    print(f"✅ Found {len(matches)} total string matches")
    print(f"🎯 {len(filtered_matches)} matches above confidence threshold")
    
    # Generate reports
    reporter = ReportGenerator()
    
    if args.format in ['text', 'both']:
        text_report = reporter.generate_summary_report(filtered_matches)
        with open(os.path.join(args.output_dir, 'extraction_report.txt'), 'w', encoding='utf-8') as f:
            f.write(text_report)
        print(f"📄 Text report saved to: {args.output_dir}/extraction_report.txt")
        
    if args.format in ['json', 'both']:
        json_report = reporter.generate_json_report(filtered_matches)
        with open(os.path.join(args.output_dir, 'extraction_report.json'), 'w', encoding='utf-8') as f:
            f.write(json_report)
        print(f"📄 JSON report saved to: {args.output_dir}/extraction_report.json")
        
    # Generate translation template for high confidence matches
    template = reporter.generate_translation_template(filtered_matches)
    with open(os.path.join(args.output_dir, 'translation_template.json'), 'w', encoding='utf-8') as f:
        f.write(template)
    print(f"📄 Translation template saved to: {args.output_dir}/translation_template.json")
    
    print("\n🎉 String extraction completed!")
    print(f"Check the reports in: {args.output_dir}")

if __name__ == "__main__":
    main()