#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add missing AI-related translations to all language files.
"""

import json
import os
from collections import OrderedDict

# Missing translations with their default values
# These will need to be properly translated for each language
MISSING_TRANSLATIONS = {
    "AI Insights": {
        "ar": "رؤى الذكاء الاصطناعي",
        "bn": "এআই অন্তর্দৃষ্টি",
        "es": "Perspectivas de IA",
        "id": "Wawasan AI",
        "ku": "ڕاڤەکانی AI",
        "ph": "Mga Pananaw ng AI",
        "ru": "ИИ Инсайты",
        "so": "Aragtida AI",
        "tr": "<PERSON><PERSON>y Zeka Görüşleri",
        "ur": "AI بصیرت"
    },
    "Admin Panel": {
        "ar": "لوحة الإدارة",
        "bn": "অ্যাডমিন প্যানেল",
        "es": "Panel de Administración",
        "id": "Panel Admin",
        "ku": "پانێلی بەڕێوەبەر",
        "ph": "Panel ng Admin",
        "ru": "Панель администратора",
        "so": "<PERSON>gga Maamul<PERSON>",
        "tr": "Yönetici Paneli",
        "ur": "ایڈمن پینل"
    },
    "What'sNewTitle": {
        "ar": "واجهة جديدة وسهلة الاستخدام:",
        "bn": "নতুন এবং ব্যবহারকারী-বান্ধব ইন্টারফেস:",
        "es": "Nueva interfaz fácil de usar:",
        "id": "Antarmuka baru dan ramah pengguna:",
        "ku": "ڕووکارێکی نوێ و ئاسان بۆ بەکارهێنان:",
        "ph": "Bago at user-friendly na interface:",
        "ru": "Новый удобный интерфейс:",
        "so": "Interface cusub oo isticmaale-saaxiibtinimo leh:",
        "tr": "Yeni ve kullanıcı dostu arayüz:",
        "ur": "نیا اور صارف دوست انٹرفیس:"
    },
    "ai_configuration": {
        "ar": "إعدادات الذكاء الاصطناعي",
        "bn": "এআই কনফিগারেশন",
        "es": "Configuración de IA",
        "id": "Konfigurasi AI",
        "ku": "ڕێکخستنی AI",
        "ph": "Kumpigurasyon ng AI",
        "ru": "Конфигурация ИИ",
        "so": "Qaabeynta AI",
        "tr": "Yapay Zeka Yapılandırması",
        "ur": "AI کی ترتیب"
    },
    "ai_error": {
        "ar": "خطأ في تحميل رؤى الذكاء الاصطناعي",
        "bn": "এআই অন্তর্দৃষ্টি লোড করতে ত্রুটি",
        "es": "Error al cargar perspectivas de IA",
        "id": "Kesalahan memuat wawasan AI",
        "ku": "هەڵە لە بارکردنی ڕاڤەکانی AI",
        "ph": "Error sa pag-load ng mga pananaw ng AI",
        "ru": "Ошибка загрузки ИИ инсайтов",
        "so": "Khalad ku saabsan soo rarista aragtida AI",
        "tr": "Yapay zeka görüşlerini yüklerken hata",
        "ur": "AI بصیرت لوڈ کرنے میں خرابی"
    },
    "ai_insights_for_verse": {
        "ar": "رؤى الذكاء الاصطناعي للآية",
        "bn": "আয়াতের জন্য এআই অন্তর্দৃষ্টি",
        "es": "Perspectivas de IA para el verso",
        "id": "Wawasan AI untuk Ayat",
        "ku": "ڕاڤەکانی AI بۆ ئایەت",
        "ph": "Mga Pananaw ng AI para sa Talata",
        "ru": "ИИ инсайты для аята",
        "so": "Aragtida AI ee Aayadda",
        "tr": "Ayet için Yapay Zeka Görüşleri",
        "ur": "آیت کے لیے AI بصیرت"
    },
    "generate_insights": {
        "ar": "توليد الرؤى",
        "bn": "অন্তর্দৃষ্টি তৈরি করুন",
        "es": "Generar perspectivas",
        "id": "Hasilkan Wawasan",
        "ku": "دروستکردنی ڕاڤەکان",
        "ph": "Bumuo ng mga Pananaw",
        "ru": "Создать инсайты",
        "so": "Abuur Aragtiyo",
        "tr": "Görüş Oluştur",
        "ur": "بصیرت پیدا کریں"
    },
    "generating": {
        "ar": "جارٍ التوليد...",
        "bn": "তৈরি হচ্ছে...",
        "es": "Generando...",
        "id": "Menghasilkan...",
        "ku": "دروست دەکرێت...",
        "ph": "Bumubuo...",
        "ru": "Генерация...",
        "so": "Waa la abuurayaa...",
        "tr": "Oluşturuluyor...",
        "ur": "تیار ہو رہا ہے..."
    },
    "loading_ai": {
        "ar": "جارٍ تحميل رؤى الذكاء الاصطناعي...",
        "bn": "এআই অন্তর্দৃষ্টি লোড হচ্ছে...",
        "es": "Cargando perspectivas de IA...",
        "id": "Memuat wawasan AI...",
        "ku": "ڕاڤەکانی AI بار دەکرێت...",
        "ph": "Nilo-load ang mga pananaw ng AI...",
        "ru": "Загрузка ИИ инсайтов...",
        "so": "Soo raridda aragtida AI...",
        "tr": "Yapay zeka görüşleri yükleniyor...",
        "ur": "AI بصیرت لوڈ ہو رہی ہے..."
    },
    "pending_validation": {
        "ar": "في انتظار التحقق",
        "bn": "যাচাইকরণ অপেক্ষমাণ",
        "es": "Validación pendiente",
        "id": "Menunggu Validasi",
        "ku": "چاوەڕوانی پشتڕاستکردنەوە",
        "ph": "Naghihintay ng Pagpapatunay",
        "ru": "Ожидает проверки",
        "so": "Sugitaanka Xaqiijinta",
        "tr": "Doğrulama Bekleniyor",
        "ur": "توثیق کا انتظار"
    },
    "retry": {
        "ar": "إعادة المحاولة",
        "bn": "পুনরায় চেষ্টা করুন",
        "es": "Reintentar",
        "id": "Coba lagi",
        "ku": "دووبارە هەوڵبدە",
        "ph": "Subukan Muli",
        "ru": "Повторить",
        "so": "Isku day mar kale",
        "tr": "Tekrar dene",
        "ur": "دوبارہ کوشش کریں"
    },
    "scholar_review": {
        "ar": "مراجعة العلماء",
        "bn": "পণ্ডিত পর্যালোচনা",
        "es": "Revisión de eruditos",
        "id": "Ulasan Ulama",
        "ku": "پێداچوونەوەی زانایان",
        "ph": "Pagsusuri ng mga Iskolar",
        "ru": "Проверка учеными",
        "so": "Dib u eegista Culimada",
        "tr": "Alim İncelemesi",
        "ur": "علماء کی جانچ"
    },
    "scholar_validated": {
        "ar": "تم التحقق من قبل العلماء",
        "bn": "পণ্ডিত দ্বারা যাচাইকৃত",
        "es": "Validado por eruditos",
        "id": "Divalidasi oleh Ulama",
        "ku": "لەلایەن زانایانەوە پشتڕاستکراوەتەوە",
        "ph": "Napatunayan ng mga Iskolar",
        "ru": "Проверено учеными",
        "so": "Waxaa xaqiijiyay Culimada",
        "tr": "Alimler tarafından doğrulandı",
        "ur": "علماء کی طرف سے تصدیق شدہ"
    },
    "validate_by_scholars": {
        "ar": "التحقق من قبل العلماء",
        "bn": "পণ্ডিতদের দ্বারা যাচাই করুন",
        "es": "Validar por eruditos",
        "id": "Validasi oleh Ulama",
        "ku": "پشتڕاستکردنەوە لەلایەن زانایانەوە",
        "ph": "Patunayan ng mga Iskolar",
        "ru": "Проверить учеными",
        "so": "Xaqiiji Culimada",
        "tr": "Alimler tarafından doğrula",
        "ur": "علماء سے تصدیق کروائیں"
    }
}

def add_missing_translations():
    """Add missing translations to all language files."""
    locales_dir = "assets/locales"
    
    # Language codes (excluding English as it already has all keys)
    languages = ["ar", "bn", "es", "id", "ku", "ph", "ru", "so", "tr", "ur"]
    
    for lang in languages:
        file_path = os.path.join(locales_dir, f"{lang}.json")
        
        # Read existing translations
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f, object_pairs_hook=OrderedDict)
        
        # Add missing translations
        added_count = 0
        for key, translations in MISSING_TRANSLATIONS.items():
            if key not in data:
                # Add the key and its metadata
                data[key] = translations[lang]
                # Add metadata key if it doesn't exist
                metadata_key = f"@{key}"
                if metadata_key not in data:
                    data[metadata_key] = {
                        "type": "text",
                        "placeholders": {}
                    }
                added_count += 1
                print(f"Added '{key}' to {lang}.json")
        
        # Sort keys to maintain consistency
        sorted_data = OrderedDict()
        keys = list(data.keys())
        # Sort regular keys and metadata keys together
        keys.sort(key=lambda x: (x.replace('@', ''), x.startswith('@')))
        
        for key in keys:
            sorted_data[key] = data[key]
        
        # Write back the updated translations
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(sorted_data, f, ensure_ascii=False, indent=2)
        
        print(f"Updated {lang}.json - Added {added_count} translations\n")

if __name__ == "__main__":
    print("Adding missing AI-related translations to all language files...\n")
    add_missing_translations()
    print("Done! All language files have been updated with AI translations.")