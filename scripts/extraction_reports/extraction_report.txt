================================================================================
STRING EXTRACTION UTILITY REPORT
================================================================================
Total files analyzed: 3
Total strings found: 47
High confidence (≥0.7): 43
Medium confidence (0.4-0.7): 4
Low confidence (<0.4): 0

HIGH CONFIDENCE MATCHES (Should definitely be localized)
------------------------------------------------------------
File: lib/presentation/screens/auth/auth_wrapper.dart:50
String: 'Retry'
Suggested key: retry
Context: child: const Text('Retry'),
Confidence: 0.80

File: lib/presentation/screens/auth/auth_wrapper.dart:121
String: 'Enable Cloud Sync'
Suggested key: enable_cloud_sync
Context: 'Enable Cloud Sync',
Confidence: 0.80

File: lib/presentation/screens/auth/auth_wrapper.dart:128
String: 'Sign up to sync your bookmarks and progress across all devices'
Suggested key: sign_up_to_sync_your
Context: 'Sign up to sync your bookmarks and progress across all devices',
Confidence: 0.80

File: lib/presentation/screens/auth/auth_wrapper.dart:147
String: 'Maybe Later'
Suggested key: maybe_later
Context: child: const Text('Maybe Later'),
Confidence: 1.00

File: lib/presentation/screens/auth/auth_wrapper.dart:152
String: 'Sign Up'
Suggested key: sign_up
Context: child: const Text('Sign Up'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:76
String: 'Sign In'
Suggested key: sign_in
Context: title: const Text('Sign In'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:102
String: 'Welcome Back'
Suggested key: welcome_back
Context: 'Welcome Back',
Confidence: 0.80

File: lib/presentation/screens/auth/login_screen.dart:108
String: 'Sign in to sync your data across devices'
Suggested key: sign_in_to_sync_your_data_across_devices
Context: 'Sign in to sync your data across devices',
Confidence: 0.80

File: lib/presentation/screens/auth/login_screen.dart:127
String: 'Please enter your email'
Suggested key: please_enter_your_email
Context: return 'Please enter your email';
Confidence: 0.80

File: lib/presentation/screens/auth/login_screen.dart:130
String: 'Please enter a valid email'
Suggested key: please_enter_a_valid_email
Context: return 'Please enter a valid email';
Confidence: 0.80

File: lib/presentation/screens/auth/login_screen.dart:159
String: 'Please enter your password'
Suggested key: please_enter_your_password
Context: return 'Please enter your password';
Confidence: 0.80

File: lib/presentation/screens/auth/login_screen.dart:171
String: 'Forgot Password?'
Suggested key: forgot_password
Context: child: const Text('Forgot Password?'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:188
String: 'Sign In'
Suggested key: sign_in
Context: : const Text('Sign In'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:212
String: 'Continue with Google'
Suggested key: continue_with_google
Context: label: const Text('Continue with Google'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:222
String: 'Continue with Apple'
Suggested key: continue_with_apple
Context: label: const Text('Continue with Apple'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:233
String: "Don't have an account?"
Suggested key: dont_have_an_account
Context: const Text("Don't have an account?"),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:236
String: 'Sign Up'
Suggested key: sign_up
Context: child: const Text('Sign Up'),
Confidence: 1.00

File: lib/presentation/screens/auth/login_screen.dart:244
String: 'Continue without account'
Suggested key: continue_without_account
Context: child: const Text('Continue without account'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:43
String: 'Please agree to the terms and conditions'
Suggested key: please_agree_to_the_terms_and_conditions
Context: content: Text('Please agree to the terms and conditions'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:81
String: 'Account created! Your data is now synced to the cloud.'
Suggested key: account_created_your_data_is
Context: ? 'Account created! Your data is now synced to the cloud.'
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:82
String: 'Account created! Please check your email to verify.'
Suggested key: account_created_please_check_your_email_to_verify
Context: : 'Account created! Please check your email to verify.',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:110
String: 'Enable Cloud Sync'
Suggested key: enable_cloud_sync
Context: title: Text(widget.isConvertingAnonymous ? 'Enable Cloud Sync' : 'Create Account'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:110
String: 'Create Account'
Suggested key: create_account
Context: title: Text(widget.isConvertingAnonymous ? 'Enable Cloud Sync' : 'Create Account'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:139
String: 'Sync Your Progress'
Suggested key: sync_your_progress
Context: ? 'Sync Your Progress'
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:140
String: 'Create Your Account'
Suggested key: create_your_account
Context: : 'Create Your Account',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:149
String: 'Create an account to sync your bookmarks and progress across all your devices'
Suggested key: create_an_account_to_sync
Context: ? 'Create an account to sync your bookmarks and progress across all your devices'
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:150
String: 'Join to save your progress and sync across devices'
Suggested key: join_to_save_your_progress_and_sync_across_devices
Context: : 'Join to save your progress and sync across devices',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:167
String: 'All your local data will be automatically synced after signup'
Suggested key: all_your_local_data_will
Context: 'All your local data will be automatically synced after signup',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:184
String: 'Full Name'
Suggested key: full_name
Context: labelText: 'Full Name',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:190
String: 'Please enter your name'
Suggested key: please_enter_your_name
Context: return 'Please enter your name';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:210
String: 'Please enter your email'
Suggested key: please_enter_your_email
Context: return 'Please enter your email';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:213
String: 'Please enter a valid email'
Suggested key: please_enter_a_valid_email
Context: return 'Please enter a valid email';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:242
String: 'Please enter a password'
Suggested key: please_enter_a_password
Context: return 'Please enter a password';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:245
String: 'Password must be at least 6 characters'
Suggested key: password_must_be_at_least_6_characters
Context: return 'Password must be at least 6 characters';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:258
String: 'Confirm Password'
Suggested key: confirm_password
Context: labelText: 'Confirm Password',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:274
String: 'Please confirm your password'
Suggested key: please_confirm_your_password
Context: return 'Please confirm your password';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:277
String: 'Passwords do not match'
Suggested key: passwords_do_not_match
Context: return 'Passwords do not match';
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:303
String: 'I agree to the Terms of Service and Privacy Policy'
Suggested key: i_agree_to_the_terms_of_service_and_privacy_policy
Context: 'I agree to the Terms of Service and Privacy Policy',
Confidence: 0.80

File: lib/presentation/screens/auth/signup_screen.dart:324
String: 'Create Account & Sync'
Suggested key: create_account_sync
Context: : Text(widget.isConvertingAnonymous ? 'Create Account & Sync' : 'Sign Up'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:324
String: 'Sign Up'
Suggested key: sign_up
Context: : Text(widget.isConvertingAnonymous ? 'Create Account & Sync' : 'Sign Up'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:333
String: 'Already have an account?'
Suggested key: already_have_an_account
Context: const Text('Already have an account?'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:336
String: 'Sign In'
Suggested key: sign_in
Context: child: const Text('Sign In'),
Confidence: 1.00

File: lib/presentation/screens/auth/signup_screen.dart:344
String: 'Continue without account'
Suggested key: continue_without_account
Context: child: const Text('Continue without account'),
Confidence: 1.00
