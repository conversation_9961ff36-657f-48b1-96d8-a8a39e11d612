{"summary": {"total_matches": 47, "high_confidence": 43, "medium_confidence": 4, "low_confidence": 0}, "matches": [{"file_path": "lib/presentation/screens/auth/auth_wrapper.dart", "line_number": 50, "original_string": "'Retry'", "suggested_key": "retry", "context": "child: const Text('Retry'),", "confidence": 0.7999999999999999}, {"file_path": "lib/presentation/screens/auth/auth_wrapper.dart", "line_number": 121, "original_string": "'Enable Cloud Sync'", "suggested_key": "enable_cloud_sync", "context": "'Enable Cloud Sync',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/auth_wrapper.dart", "line_number": 128, "original_string": "'Sign up to sync your bookmarks and progress across all devices'", "suggested_key": "sign_up_to_sync_your", "context": "'Sign up to sync your bookmarks and progress across all devices',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/auth_wrapper.dart", "line_number": 147, "original_string": "'Maybe Later'", "suggested_key": "maybe_later", "context": "child: const Text('Maybe Later'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/auth_wrapper.dart", "line_number": 152, "original_string": "'Sign Up'", "suggested_key": "sign_up", "context": "child: const Text('Sign Up'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 76, "original_string": "'Sign In'", "suggested_key": "sign_in", "context": "title: const Text('Sign In'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 102, "original_string": "'Welcome Back'", "suggested_key": "welcome_back", "context": "'Welcome Back',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 108, "original_string": "'Sign in to sync your data across devices'", "suggested_key": "sign_in_to_sync_your_data_across_devices", "context": "'Sign in to sync your data across devices',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 121, "original_string": "'Email'", "suggested_key": "email", "context": "labelText: 'Email',", "confidence": 0.6}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 127, "original_string": "'Please enter your email'", "suggested_key": "please_enter_your_email", "context": "return 'Please enter your email';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 130, "original_string": "'Please enter a valid email'", "suggested_key": "please_enter_a_valid_email", "context": "return 'Please enter a valid email';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 143, "original_string": "'Password'", "suggested_key": "password", "context": "labelText: 'Password',", "confidence": 0.6}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 159, "original_string": "'Please enter your password'", "suggested_key": "please_enter_your_password", "context": "return 'Please enter your password';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 171, "original_string": "'Forgot Password?'", "suggested_key": "forgot_password", "context": "child: const Text('Forgot Password?'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 188, "original_string": "'Sign In'", "suggested_key": "sign_in", "context": ": const Text('Sign In'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 212, "original_string": "'Continue with Google'", "suggested_key": "continue_with_google", "context": "label: const Text('Continue with Google'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 222, "original_string": "'Continue with Apple'", "suggested_key": "continue_with_apple", "context": "label: const Text('Continue with Apple'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 233, "original_string": "\"Don't have an account?\"", "suggested_key": "dont_have_an_account", "context": "const Text(\"Don't have an account?\"),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 236, "original_string": "'Sign Up'", "suggested_key": "sign_up", "context": "child: const Text('Sign Up'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/login_screen.dart", "line_number": 244, "original_string": "'Continue without account'", "suggested_key": "continue_without_account", "context": "child: const Text('Continue without account'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 43, "original_string": "'Please agree to the terms and conditions'", "suggested_key": "please_agree_to_the_terms_and_conditions", "context": "content: Text('Please agree to the terms and conditions'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 81, "original_string": "'Account created! Your data is now synced to the cloud.'", "suggested_key": "account_created_your_data_is", "context": "? 'Account created! Your data is now synced to the cloud.'", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 82, "original_string": "'Account created! Please check your email to verify.'", "suggested_key": "account_created_please_check_your_email_to_verify", "context": ": 'Account created! Please check your email to verify.',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 110, "original_string": "'Enable Cloud Sync'", "suggested_key": "enable_cloud_sync", "context": "title: Text(widget.isConvertingAnonymous ? 'Enable Cloud Sync' : 'Create Account'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 110, "original_string": "'Create Account'", "suggested_key": "create_account", "context": "title: Text(widget.isConvertingAnonymous ? 'Enable Cloud Sync' : 'Create Account'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 139, "original_string": "'Sync Your Progress'", "suggested_key": "sync_your_progress", "context": "? 'Sync Your Progress'", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 140, "original_string": "'Create Your Account'", "suggested_key": "create_your_account", "context": ": 'Create Your Account',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 149, "original_string": "'Create an account to sync your bookmarks and progress across all your devices'", "suggested_key": "create_an_account_to_sync", "context": "? 'Create an account to sync your bookmarks and progress across all your devices'", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 150, "original_string": "'Join to save your progress and sync across devices'", "suggested_key": "join_to_save_your_progress_and_sync_across_devices", "context": ": 'Join to save your progress and sync across devices',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 167, "original_string": "'All your local data will be automatically synced after signup'", "suggested_key": "all_your_local_data_will", "context": "'All your local data will be automatically synced after signup',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 184, "original_string": "'Full Name'", "suggested_key": "full_name", "context": "labelText: 'Full Name',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 190, "original_string": "'Please enter your name'", "suggested_key": "please_enter_your_name", "context": "return 'Please enter your name';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 204, "original_string": "'Email'", "suggested_key": "email", "context": "labelText: 'Email',", "confidence": 0.6}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 210, "original_string": "'Please enter your email'", "suggested_key": "please_enter_your_email", "context": "return 'Please enter your email';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 213, "original_string": "'Please enter a valid email'", "suggested_key": "please_enter_a_valid_email", "context": "return 'Please enter a valid email';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 226, "original_string": "'Password'", "suggested_key": "password", "context": "labelText: 'Password',", "confidence": 0.6}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 242, "original_string": "'Please enter a password'", "suggested_key": "please_enter_a_password", "context": "return 'Please enter a password';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 245, "original_string": "'Password must be at least 6 characters'", "suggested_key": "password_must_be_at_least_6_characters", "context": "return 'Password must be at least 6 characters';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 258, "original_string": "'Confirm Password'", "suggested_key": "confirm_password", "context": "labelText: 'Confirm Password',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 274, "original_string": "'Please confirm your password'", "suggested_key": "please_confirm_your_password", "context": "return 'Please confirm your password';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 277, "original_string": "'Passwords do not match'", "suggested_key": "passwords_do_not_match", "context": "return 'Passwords do not match';", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 303, "original_string": "'I agree to the Terms of Service and Privacy Policy'", "suggested_key": "i_agree_to_the_terms_of_service_and_privacy_policy", "context": "'I agree to the Terms of Service and Privacy Policy',", "confidence": 0.8}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 324, "original_string": "'Create Account & Sync'", "suggested_key": "create_account_sync", "context": ": Text(widget.isConvertingAnonymous ? 'Create Account & Sync' : 'Sign Up'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 324, "original_string": "'Sign Up'", "suggested_key": "sign_up", "context": ": Text(widget.isConvertingAnonymous ? 'Create Account & Sync' : 'Sign Up'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 333, "original_string": "'Already have an account?'", "suggested_key": "already_have_an_account", "context": "const Text('Already have an account?'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 336, "original_string": "'Sign In'", "suggested_key": "sign_in", "context": "child: const Text('Sign In'),", "confidence": 1.0}, {"file_path": "lib/presentation/screens/auth/signup_screen.dart", "line_number": 344, "original_string": "'Continue without account'", "suggested_key": "continue_without_account", "context": "child: const Text('Continue without account'),", "confidence": 1.0}]}