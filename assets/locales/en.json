{"AH": "AH", "@AH": {"type": "text", "placeholders": {}}, "AI Insights": "AI Insights", "Admin Panel": "Admin Panel", "Asr": "<PERSON><PERSON>", "@Asr": {"type": "text", "placeholders": {}}, "Day": "Day", "@Day": {"type": "text", "placeholders": {}}, "Days": "Days", "@Days": {"type": "text", "placeholders": {}}, "Dhu Al-Hijjah": "<PERSON>hu <PERSON>", "@Dhu Al-Hijjah": {"type": "text", "placeholders": {}}, "Dhu Al-Qi'dah": "<PERSON><PERSON>", "@Dhu Al-Qi'dah": {"type": "text", "placeholders": {}}, "Dhuhr": "<PERSON><PERSON><PERSON>", "@Dhuhr": {"type": "text", "placeholders": {}}, "EidAl-Adha": "<PERSON><PERSON>", "@EidAl-Adha": {"type": "text", "placeholders": {}}, "EidAl-Fitr": "<PERSON><PERSON>", "@EidAl-Fitr": {"type": "text", "placeholders": {}}, "Fajr": "<PERSON><PERSON><PERSON>", "@Fajr": {"type": "text", "placeholders": {}}, "Fri": "<PERSON><PERSON>", "Friday": "Friday", "@Friday": {"type": "text", "placeholders": {}}, "Isha": "<PERSON><PERSON>", "@Isha": {"type": "text", "placeholders": {}}, "Jumada Al-Awwal": "<PERSON><PERSON>", "@Jumada Al-Awwal": {"type": "text", "placeholders": {}}, "Jumada Al-Thani": "<PERSON><PERSON>", "@Jumada Al-Thani": {"type": "text", "placeholders": {}}, "Last Third": "The last third", "@Last Third": {"type": "text", "placeholders": {}}, "Maghrib": "<PERSON><PERSON><PERSON><PERSON>", "@Maghrib": {"type": "text", "placeholders": {}}, "Meccan": "Meccan", "@Meccan": {"type": "text", "placeholders": {}}, "Medinan": "Medinan", "@Medinan": {"type": "text", "placeholders": {}}, "Middle of the Night": "Midnight", "@Middle of the Night": {"type": "text", "placeholders": {}}, "Mon": "Mon", "Monday": "Monday", "@Monday": {"type": "text", "placeholders": {}}, "Muharram": "<PERSON><PERSON><PERSON>", "@Muharram": {"type": "text", "placeholders": {}}, "Rabi' Al-Awwal": "<PERSON><PERSON><PERSON>", "@Rabi' Al-Awwal": {"type": "text", "placeholders": {}}, "Rabi' Al-Thani": "<PERSON><PERSON><PERSON>", "@Rabi' Al-Thani": {"type": "text", "placeholders": {}}, "Rajab": "<PERSON><PERSON>", "@Rajab": {"type": "text", "placeholders": {}}, "Ramadan": "<PERSON><PERSON>", "@Ramadan": {"type": "text", "placeholders": {}}, "RemainsUntilTheEndOf": "Remains until the end of", "@RemainsUntilTheEndOf": {"type": "text", "placeholders": {}}, "Safar": "<PERSON><PERSON>", "@Safar": {"type": "text", "placeholders": {}}, "Sat": "Sat", "Saturday": "Saturday", "@Saturday": {"type": "text", "placeholders": {}}, "Sha'aban": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@Sha'aban": {"type": "text", "placeholders": {}}, "Shawwal": "<PERSON><PERSON>", "@Shawwal": {"type": "text", "placeholders": {}}, "Sun": "Sun", "Sunday": "Sunday", "@Sunday": {"type": "text", "placeholders": {}}, "Sunrise": "Sunrise", "@Sunrise": {"type": "text", "placeholders": {}}, "Thu": "<PERSON>hu", "Thursday": "Thursday", "@Thursday": {"type": "text", "placeholders": {}}, "Tue": "<PERSON><PERSON>", "Tuesday": "Tuesday", "@Tuesday": {"type": "text", "placeholders": {}}, "Wed": "Wed", "Wednesday": "Wednesday", "@Wednesday": {"type": "text", "placeholders": {}}, "What's New": "What's New", "@What's New": {"type": "text", "placeholders": {}}, "What'sNewDetails10": "۞ Added support for Kurdish, Turkish, and Russian languages in the app.\n۞ Added more readers.", "@What'sNewDetails10": {"type": "text", "placeholders": {}}, "What'sNewTitle": "New and user-friendly interface:", "@What'sNewTitle": {"type": "text", "placeholders": {}}, "aboutApp": "About App", "@aboutApp": {"type": "text", "placeholders": {}}, "aboutBook": "About the Book", "@aboutBook": {"type": "text", "placeholders": {}}, "aboutSurah": "About the Surah", "@aboutSurah": {"type": "text", "placeholders": {}}, "about_app": "The \"Holy Quran - Hikma Library\" app is an integrated application that offers a wide range of features to enhance the experience of reading and learning the Holy Quran, including:", "@about_app": {"type": "text", "placeholders": {}}, "about_app2": "Among the most important features of the application :", "@about_app2": {"type": "text", "placeholders": {}}, "about_app3": "۞ A new and user-friendly interface: The application has been designed with a modern and intuitive user interface that makes it easy for users to navigate and access different features.\n۞ Adoption of the King Fahd Complex edition: The application uses the edition of the King Fahd Complex for the printing of the Holy Quran, known for its reliability and perfection, ensuring the accuracy and integrity of the text.\n۞ Interactive and integrated reading: The application provides the possibility of interactive reading with the electronic Quran text, in addition to listening to recitations, and studying and memorizing the Holy Quran easily.\n۞ Flexible reading: Users can read the Quran as if they were reading a paper copy or choose the single verse mode for greater focus.\n۞ Textual search feature: The application contains an instant search feature in the verses of the Quran, with the ability to go directly to the desired page or surah.\n۞ Adding bookmark badges: This feature allows saving pages or verses for easy reference at any time.\n۞ Listening to verses: The application provides the possibility to listen to each verse by a number of famous reciters.\n۞ Interpretation and translation of verses: Users can access the interpretation or translation of each verse, and switch between interpretations as desired.\n۞ Easy navigation between surahs: The application facilitates smooth and quick navigation between surahs.\n۞ Reading stop signs: This feature helps to understand the appropriate places to stop while reading.\n۞ Fortress of the Muslim Dhikr: It is possible to read the complete Fortress of the Muslim Dhikr and navigate between sections easily, with the possibility of adding Dhikr to favorites.\n۞ Changing color styles: The application supports changing color styles, including the dark mode, to improve the reading experience.\n۞ Listening and downloading: It is possible to listen to the surahs or download them for later listening without the need for the internet.\n\nThese features make the \"Holy Quran - Hikma Library\" app a comprehensive tool for anyone wishing to read, learn, and contemplate the Holy Quran in an easy and effective way.", "@about_app3": {"type": "text", "placeholders": {}}, "about_us": "About App", "@about_us": {"type": "text", "placeholders": {}}, "account_created_synced": "Account created! Your data is now synced to the cloud.", "@account_created_synced": {"type": "text", "placeholders": {}}, "account_created_verify_email": "Account created! Please check your email to verify.", "@account_created_verify_email": {"type": "text", "placeholders": {}}, "addBookmark": "Bookmark added!", "@addBookmark": {"type": "text", "placeholders": {}}, "addKhatmah": "Add a <PERSON>", "@addKhatmah": {"type": "text", "placeholders": {}}, "addMore": "Add more", "@addMore": {"type": "text", "placeholders": {}}, "addReminder": "Add <PERSON> Reminder", "@addReminder": {"type": "text", "placeholders": {}}, "addToBookmark": "Add To Bookmark", "@addToBookmark": {"type": "text", "placeholders": {}}, "addZekrBookmark": "The Zekr has been added to favourites", "@addZekrBookmark": {"type": "text", "placeholders": {}}, "add_new_bookmark": "Add a new Bookmark", "@add_new_bookmark": {"type": "text", "placeholders": {}}, "add_new_note": "Add a new note", "@add_new_note": {"type": "text", "placeholders": {}}, "agree_terms": "I agree to the Terms of Service and Privacy Policy", "@agree_terms": {"type": "text", "placeholders": {}}, "ai_configuration": "AI Configuration", "ai_error": "Error loading AI insights", "ai_insights_for_verse": "AI Insights for Verse", "alheekmahlib": "Alheekmah Library", "@alheekmahlib": {"type": "text", "placeholders": {}}, "allBooks": "All Books", "@allBooks": {"type": "text", "placeholders": {}}, "allJuz": "Parts", "@allJuz": {"type": "text", "placeholders": {}}, "already_have_account": "Already have an account?", "@already_have_account": {"type": "text", "placeholders": {}}, "appLang": "App Language", "@appLang": {"type": "text", "placeholders": {}}, "appName": "Quranic Insights", "@appName": {"description": "appName", "type": "text", "placeholders": {}}, "arafah": "Day of Arafah", "@arafah": {"type": "text", "placeholders": {}}, "arafahReminder": "Reminder for the Fasting on the Day of Arafah", "@arafahReminder": {"type": "text", "placeholders": {}}, "ashura": "<PERSON><PERSON>", "@ashura": {"type": "text", "placeholders": {}}, "aya_count": "Number of verses", "@aya_count": {"description": "aya_count", "type": "text", "placeholders": {}}, "ayahs": "<PERSON><PERSON><PERSON>", "@ayahs": {"type": "text", "placeholders": {}}, "azkar": "<PERSON><PERSON><PERSON>", "@azkar": {"type": "text", "placeholders": {}}, "azkarfav": "<PERSON><PERSON><PERSON>", "@azkarfav": {"type": "text", "placeholders": {}}, "backTo": "Back To", "@backTo": {"type": "text", "placeholders": {}}, "blueMode": "Blue", "@blueMode": {"type": "text", "placeholders": {}}, "bookmark": "Bookmarks", "@bookmark": {"type": "text", "placeholders": {}}, "bookmark_title": "Bookmark Name", "@bookmark_title": {"type": "text", "placeholders": {}}, "bookmarks": "Bookmarks Saved", "@bookmarks": {"type": "text", "placeholders": {}}, "bookmarksList": "Bookmarks List", "@bookmarksList": {"type": "text", "placeholders": {}}, "booksDeleted": "The book has been deleted.", "@booksDeleted": {"type": "text", "placeholders": {}}, "booksDownloaded": "The book has been downloaded.", "@booksDownloaded": {"type": "text", "placeholders": {}}, "brown": "<PERSON>", "@brown": {"type": "text", "placeholders": {}}, "brownMode": "<PERSON>", "@brownMode": {"type": "text", "placeholders": {}}, "calendar": "Calendar", "calenderSettings": "Calendar Settings", "@calenderSettings": {"type": "text", "placeholders": {}}, "cancel": "Cancel", "@cancel": {"type": "text", "placeholders": {}}, "chapterBook": "Chapters of the Book", "@chapterBook": {"type": "text", "placeholders": {}}, "choiceAyah": "Please choose the verse first!", "@choiceAyah": {"type": "text", "placeholders": {}}, "choiceBackgroundColor": "Choose background color", "@choiceBackgroundColor": {"type": "text", "placeholders": {}}, "choiceColor": "Choose the color", "@choiceColor": {"type": "text", "placeholders": {}}, "chooseDhekr": "Set a time for reflection.", "@chooseDhekr": {"type": "text", "placeholders": {}}, "choseQuran": "Choose Reading Mode", "@choseQuran": {"type": "text", "placeholders": {}}, "close": "Close", "@close": {"type": "text", "placeholders": {}}, "confirm_password": "Confirm Password", "@confirm_password": {"type": "text", "placeholders": {}}, "continue_with_apple": "Continue with Apple", "@continue_with_apple": {"type": "text", "placeholders": {}}, "continue_with_google": "Continue with Google", "@continue_with_google": {"type": "text", "placeholders": {}}, "continue_without_account": "Continue without account", "@continue_without_account": {"type": "text", "placeholders": {}}, "copy": "Copy", "@copy": {"type": "text", "placeholders": {}}, "copyAyah": "The verse has been copied", "@copyAyah": {"type": "text", "placeholders": {}}, "copyAzkarText": "The Azkar has been copied!", "@copyAzkarText": {"type": "text", "placeholders": {}}, "copyTafseer": "The Tafseer has been copied", "@copyTafseer": {"type": "text", "placeholders": {}}, "createKhatmah": "Create the Khatmah", "@createKhatmah": {"type": "text", "placeholders": {}}, "createPlayList": "Create the playlist", "@createPlayList": {"type": "text", "placeholders": {}}, "create_account": "Create Account", "@create_account": {"type": "text", "placeholders": {}}, "create_account_and_sync": "Create Account & Sync", "@create_account_and_sync": {"type": "text", "placeholders": {}}, "create_account_to_sync": "Create an account to sync your bookmarks and progress across all your devices", "@create_account_to_sync": {"type": "text", "placeholders": {}}, "customReminder": "Custom reminder", "@customReminder": {"type": "text", "placeholders": {}}, "dailyZeker": "Prayer of the Day", "@dailyZeker": {"type": "text", "placeholders": {}}, "dark": "Dark Mode", "@dark": {"type": "text", "placeholders": {}}, "darkMode": "Dark", "@darkMode": {"type": "text", "placeholders": {}}, "defaultFontText": "<PERSON><PERSON><PERSON>", "@defaultFontText": {"type": "text", "placeholders": {}}, "delete": "Delete", "@delete": {"type": "text", "placeholders": {}}, "deletedBookmark": "Bookmark deleted!", "@deletedBookmark": {"type": "text", "placeholders": {}}, "deletedPlayList": "The playlist has been deleted", "@deletedPlayList": {"type": "text", "placeholders": {}}, "deletedReminder": "Reminder deleted!", "@deletedReminder": {"type": "text", "placeholders": {}}, "deletedZekrBookmark": "The Zekr has been removed from favourites!", "@deletedZekrBookmark": {"type": "text", "placeholders": {}}, "divisionBySahabah": "Division by the Sahabah", "@divisionBySahabah": {"type": "text", "placeholders": {}}, "dont_have_account": "Don't have an account?", "@dont_have_account": {"type": "text", "placeholders": {}}, "download": "Download", "@download": {"type": "text", "placeholders": {}}, "downloadBookFirst": "Please download the book first.", "@downloadBookFirst": {"type": "text", "placeholders": {}}, "downloadedFontsText": "Quran Font", "@downloadedFontsText": {"type": "text", "placeholders": {}}, "downloading": "Downloading...", "@downloading": {"type": "text", "placeholders": {}}, "duration": "Duration", "@duration": {"type": "text", "placeholders": {}}, "edit": "Edit", "@edit": {"type": "text", "placeholders": {}}, "editHijriDay": "Edit <PERSON><PERSON><PERSON>", "@editHijriDay": {"type": "text", "placeholders": {}}, "eidGreetingContent": "May Allah accept from us and from you fasting, prayer, and righteous deeds.", "@eidGreetingContent": {"type": "text", "placeholders": {}}, "eidGreetingContent2": "Every year and you are closer to <PERSON>.", "@eidGreetingContent2": {"type": "text", "placeholders": {}}, "eidGreetingTitle": "<PERSON><PERSON>", "@eidGreetingTitle": {"type": "text", "placeholders": {}}, "email": "Contact us", "@email": {"type": "text", "placeholders": {}}, "enable_cloud_sync": "Enable Cloud Sync", "@enable_cloud_sync": {"type": "text", "placeholders": {}}, "events": "Events", "facebook": "Follow us on Facebook", "@facebook": {"type": "text", "placeholders": {}}, "fillAllFields": "Please fill in the fields!", "@fillAllFields": {"type": "text", "placeholders": {}}, "fontSize": "Change Font Size", "@fontSize": {"type": "text", "placeholders": {}}, "fonts": "Fonts", "@fonts": {"type": "text", "placeholders": {}}, "fontsNotes": "For a better experience and optimal Quran appearance, please download its fonts.", "@fontsNotes": {"type": "text", "placeholders": {}}, "forgot_password": "Forgot Password?", "@forgot_password": {"type": "text", "placeholders": {}}, "from": "From", "@from": {"type": "text", "placeholders": {}}, "full_name": "Full Name", "@full_name": {"type": "text", "placeholders": {}}, "generate_insights": "Generate Insights", "generating": "Generating...", "green": "Green Mode", "@green": {"type": "text", "placeholders": {}}, "hasPassed": "Has passed", "@hasPassed": {"type": "text", "placeholders": {}}, "hijri": "<PERSON><PERSON><PERSON>", "hijriCalendar": "Hijri Calendar", "hijriNote": "Note: Please be aware that there might be a slight difference in the specified times as the Hijri calendar is based on the direct sighting of the crescent moon. We make every effort to ensure the accuracy of the announced dates, taking into account that the calendar will be corrected in case of the moon sighting.", "@hijriNote": {"type": "text", "placeholders": {}}, "hizb": "Hizb", "@hizb": {"type": "text", "placeholders": {}}, "home": "Home", "@home": {"type": "text", "placeholders": {}}, "islamicCalendar": "Islamic Calendar", "juz": "Part", "@juz": {"type": "text", "placeholders": {}}, "khatmah": "<PERSON><PERSON><PERSON>", "@khatmah": {"type": "text", "placeholders": {}}, "khatmahName": "Name of the Khatmah", "@khatmahName": {"type": "text", "placeholders": {}}, "lang": "English", "@lang": {"type": "text", "placeholders": {}}, "langChange": "Change the language", "@langChange": {"type": "text", "placeholders": {}}, "lastDayOf": "The last day of the month of", "@lastDayOf": {"type": "text", "placeholders": {}}, "lastListen": "Last Listen", "@lastListen": {"type": "text", "placeholders": {}}, "lastRead": "Last Read", "@lastRead": {"type": "text", "placeholders": {}}, "lastSearch": "Last Search", "@lastSearch": {"type": "text", "placeholders": {}}, "loading_ai": "Loading AI insights...", "menu": "<PERSON><PERSON>", "@menu": {"description": "menu", "type": "text", "placeholders": {}}, "mobileDataAyat": "Note: You are using cellular data to download verses!", "@mobileDataAyat": {"type": "text", "placeholders": {}}, "mobileDataListen": "Note: You are using cellular data to listen to surahs!", "@mobileDataListen": {"type": "text", "placeholders": {}}, "mobileDataSurahs": "Note: You are using cellular data to download the surahs!", "@mobileDataSurahs": {"type": "text", "placeholders": {}}, "month": "Month", "myLibrary": "My Library", "@myLibrary": {"type": "text", "placeholders": {}}, "next": "Next", "@next": {"type": "text", "placeholders": {}}, "nightOfQadir": "Reminder for Laylat al-Qadr", "@nightOfQadir": {"type": "text", "placeholders": {}}, "noBooksDownloaded": "You have not downloaded any book yet.", "@noBooksDownloaded": {"type": "text", "placeholders": {}}, "noInternet": "The device is not connected to the Internet!", "@noInternet": {"type": "text", "placeholders": {}}, "noNotifications": "No notifications", "@noNotifications": {"type": "text", "placeholders": {}}, "note_details": "Note Details", "@note_details": {"type": "text", "placeholders": {}}, "note_title": "Title", "@note_title": {"description": "note_title", "type": "text", "placeholders": {}}, "notes": "Notes", "@notes": {"description": "notes", "type": "text", "placeholders": {}}, "notification": "Notifications", "@notification": {"type": "text", "placeholders": {}}, "notifyAdhkarBody": "Don't forget to read @adhkarType", "@notifyAdhkarBody": {"type": "text", "placeholders": {"adhkarType": {}}}, "notifyBooksBody": "Don't forget to continue reading the book @bookName", "@notifyBooksBody": {"type": "text", "placeholders": {"bookName": {}}}, "notifyListenBody": "Don't forget to continue listening to the surahs", "@notifyListenBody": {"type": "text", "placeholders": {"bookName": {}}}, "notifyQuranBody": "You stopped at page @currentPageNumber in the Quran, would you like to continue?", "@notifyQuranBody": {"type": "text", "placeholders": {"currentPageNumber": {}}}, "ok": "Ok", "@ok": {"type": "text", "placeholders": {}}, "oldMode": "Old", "@oldMode": {"type": "text", "placeholders": {}}, "onboardDesc1": "- Ease of searching for a verse.\n- Change the language.\n- Listen to the page.\n- Change the reader.", "@onboardDesc1": {"type": "text", "placeholders": {}}, "onboardDesc2": "The interpretation of each verse can be read by pulling the list up.", "@onboardDesc2": {"type": "text", "placeholders": {}}, "onboardDesc3": "1- When you double click the page is enlarged.\n2- Upon long click you will be presented with the option to save the page.\n3- When you press once, the menus appear.", "@onboardDesc3": {"type": "text", "placeholders": {}}, "onboardTitle1": "Easy interface", "@onboardTitle1": {"type": "text", "placeholders": {}}, "onboardTitle2": "Show The Tafseer", "@onboardTitle2": {"type": "text", "placeholders": {}}, "onboardTitle3": "Click Options", "@onboardTitle3": {"type": "text", "placeholders": {}}, "online": "Online", "@online": {"type": "text", "placeholders": {}}, "or": "OR", "@or": {"type": "text", "placeholders": {}}, "ourApps": "Our Apps", "@ourApps": {"type": "text", "placeholders": {}}, "page": "Page", "@page": {"type": "text", "placeholders": {}}, "pageNo": "Page No", "@pageNo": {"type": "text", "placeholders": {}}, "pages": "Pages", "@pages": {"type": "text", "placeholders": {}}, "part": "Part", "@part": {"type": "text", "placeholders": {}}, "password": "Password", "@password": {"type": "text", "placeholders": {}}, "password_min_length": "Password must be at least 6 characters", "@password_min_length": {"type": "text", "placeholders": {}}, "passwords_dont_match": "Passwords do not match", "@passwords_dont_match": {"type": "text", "placeholders": {}}, "pauseSurah": "<PERSON><PERSON>", "@pauseSurah": {"type": "text", "placeholders": {}}, "pending_validation": "Pending Validation", "playList": "Playlists", "@playList": {"type": "text", "placeholders": {}}, "playListName": "Type the name of the playlist", "@playListName": {"type": "text", "placeholders": {}}, "please_agree_terms": "Please agree to the terms and conditions", "@please_agree_terms": {"type": "text", "placeholders": {}}, "please_confirm_password": "Please confirm your password", "@please_confirm_password": {"type": "text", "placeholders": {}}, "please_enter_email": "Please enter your email", "@please_enter_email": {"type": "text", "placeholders": {}}, "please_enter_name": "Please enter your name", "@please_enter_name": {"type": "text", "placeholders": {}}, "please_enter_password": "Please enter your password", "@please_enter_password": {"type": "text", "placeholders": {}}, "please_enter_valid_email": "Please enter a valid email", "@please_enter_valid_email": {"type": "text", "placeholders": {}}, "qibla": "Qibla Direction", "@qibla": {"type": "text", "placeholders": {}}, "quran": "The Holy Quran", "@quran": {"type": "text", "placeholders": {}}, "quranAudio": "Listening to Quran", "@quranAudio": {"type": "text", "placeholders": {}}, "quranPages": "<PERSON> (Pages)", "@quranPages": {"type": "text", "placeholders": {}}, "quranText": "<PERSON> (Ayah)", "@quranText": {"type": "text", "placeholders": {}}, "quran_sorah": "Surahs", "@quran_sorah": {"type": "text", "placeholders": {}}, "ramadhan": "Ramadan Month", "@ramadhan": {"type": "text", "placeholders": {}}, "ramadhanMubarak": "<PERSON><PERSON><PERSON>", "@ramadhanMubarak": {"type": "text", "placeholders": {}}, "readLess": "Read Less", "@readLess": {"type": "text", "placeholders": {}}, "readMore": "Read More", "@readMore": {"type": "text", "placeholders": {}}, "reader1": "<PERSON>", "@reader1": {"type": "text", "placeholders": {}}, "reader10": "<PERSON>ader <PERSON>", "@reader10": {"type": "text", "placeholders": {}}, "reader11": "<PERSON><PERSON><PERSON>", "@reader11": {"type": "text", "placeholders": {}}, "reader12": "<PERSON><PERSON><PERSON>", "@reader12": {"type": "text", "placeholders": {}}, "reader13": "Wadi <PERSON>-<PERSON>", "@reader13": {"type": "text", "placeholders": {}}, "reader14": "<PERSON><PERSON>", "@reader14": {"type": "text", "placeholders": {}}, "reader15": "<PERSON>", "@reader15": {"type": "text", "placeholders": {}}, "reader16": "<PERSON><PERSON>", "@reader16": {"type": "text", "placeholders": {}}, "reader17": "<PERSON>", "@reader17": {"type": "text", "placeholders": {}}, "reader18": "<PERSON><PERSON>", "@reader18": {"type": "text", "placeholders": {}}, "reader19": "<PERSON>", "@reader19": {"type": "text", "placeholders": {}}, "reader2": "<PERSON>", "@reader2": {"type": "text", "placeholders": {}}, "reader20": "<PERSON><PERSON>", "@reader20": {"type": "text", "placeholders": {}}, "reader3": "<PERSON><PERSON><PERSON>", "@reader3": {"type": "text", "placeholders": {}}, "reader4": "<PERSON>", "@reader4": {"type": "text", "placeholders": {}}, "reader5": "<PERSON><PERSON>", "@reader5": {"type": "text", "placeholders": {}}, "reader6": "<PERSON><PERSON>", "@reader6": {"type": "text", "placeholders": {}}, "reader7": "<PERSON><PERSON>", "@reader7": {"type": "text", "placeholders": {}}, "reader8": "<PERSON>", "@reader8": {"type": "text", "placeholders": {}}, "reader9": "<PERSON><PERSON>", "@reader9": {"type": "text", "placeholders": {}}, "reminderToFastAshura": "Reminder for the Fasting of Ashura", "@reminderToFastAshura": {"type": "text", "placeholders": {}}, "reminderToFastTasoo'a": "Reminder for the Fasting of Tasu'a", "@reminderToFastTasoo'a": {"type": "text", "placeholders": {}}, "reminders": "Reminders", "@reminders": {"type": "text", "placeholders": {}}, "repeatSurah": "<PERSON><PERSON>", "@repeatSurah": {"type": "text", "placeholders": {}}, "replaySurah": "<PERSON><PERSON>", "@replaySurah": {"type": "text", "placeholders": {}}, "reset": "Reset", "@reset": {"type": "text", "placeholders": {}}, "retry": "Retry", "sajda": "Prostration", "@sajda": {"type": "text", "placeholders": {}}, "salat": "Prayer Times", "@salat": {"type": "text", "placeholders": {}}, "save": "Save", "@save": {"type": "text", "placeholders": {}}, "scholar_review": "Scholar Review", "scholar_validated": "Scholar Validated", "search": "Search", "@search": {"type": "text", "placeholders": {}}, "searchInBooks": "Search in Books", "@searchInBooks": {"type": "text", "placeholders": {}}, "searchToSurah": "Search To Surah", "@searchToSurah": {"type": "text", "placeholders": {}}, "search_description": "You can search for all verses of the Noble Qur’an, just type a word from the verse.", "@search_description": {"type": "text", "placeholders": {}}, "search_hint": "Search the verses of the Quran", "@search_hint": {"description": "search_hint", "type": "text", "placeholders": {}}, "search_word": "Search for a Verse", "@search_word": {"type": "text", "placeholders": {}}, "selectScreen": "<PERSON>ose Start Screen", "@selectScreen": {"type": "text", "placeholders": {}}, "selectTime": "Choose the remembrance.", "@selectTime": {"type": "text", "placeholders": {}}, "select_player": "Reader selected", "@select_player": {"type": "text", "placeholders": {}}, "setting": "Setting", "@setting": {"type": "text", "placeholders": {}}, "sexShawwal": "Reminder for the Fasting of Six Days of Shawwal", "@sexShawwal": {"type": "text", "placeholders": {}}, "share": "Share The App", "@share": {"type": "text", "placeholders": {}}, "shareImage": "As an image", "@shareImage": {"type": "text", "placeholders": {}}, "shareImageWTrans": "As an image with additional content", "@shareImageWTrans": {"type": "text", "placeholders": {}}, "shareTafseer": "Share", "@shareTafseer": {"type": "text", "placeholders": {}}, "shareText": "As text", "@shareText": {"type": "text", "placeholders": {}}, "shareTrans": "Note: Sharing the interpretation in an image only supports the interpretation of Al-Saadi, because the interpretation is not lengthy.", "@shareTrans": {"type": "text", "placeholders": {}}, "sign_in": "Sign In", "@sign_in": {"type": "text", "placeholders": {}}, "sign_in_sync_message": "Sign in to sync your data across devices", "@sign_in_sync_message": {"type": "text", "placeholders": {}}, "sign_up": "Sign Up", "@sign_up": {"type": "text", "placeholders": {}}, "skip": "<PERSON><PERSON>", "@skip": {"type": "text", "placeholders": {}}, "skipToPrevious": "<PERSON>p <PERSON>", "@skipToPrevious": {"type": "text", "placeholders": {}}, "sorah": "<PERSON><PERSON>", "@sorah": {"type": "text", "placeholders": {}}, "start": "Start", "@start": {"type": "text", "placeholders": {}}, "startHijriYear": "Start of the Hijri Year", "@startHijriYear": {"type": "text", "placeholders": {}}, "startScreen": "Start Screen", "@startScreen": {"type": "text", "placeholders": {}}, "stopSigns": "Stop Signs", "@stopSigns": {"type": "text", "placeholders": {}}, "stop_title": "The noble Qur’an - Al-Heekmah Library", "@stop_title": {"type": "text", "placeholders": {}}, "surahNames": "Names of the Surah", "@surahNames": {"type": "text", "placeholders": {}}, "surahsList": "Surahs List", "@surahsList": {"type": "text", "placeholders": {}}, "sync_your_progress": "Sync Your Progress", "@sync_your_progress": {"type": "text", "placeholders": {}}, "tafBaghawyD": "<PERSON><PERSON><PERSON>", "@tafBaghawyD": {"type": "text", "placeholders": {}}, "tafBaghawyN": "<PERSON><PERSON><PERSON>", "@tafBaghawyN": {"type": "text", "placeholders": {}}, "tafChange": "Choose an tafsir", "@tafChange": {"type": "text", "placeholders": {}}, "tafIbnkatheerD": "<PERSON><PERSON><PERSON> <PERSON><PERSON> al-Azi<PERSON>", "@tafIbnkatheerD": {"type": "text", "placeholders": {}}, "tafIbnkatheerN": "<PERSON><PERSON><PERSON>", "@tafIbnkatheerN": {"type": "text", "placeholders": {}}, "tafQurtubiD": "<PERSON><PERSON><PERSON><PERSON> <PERSON>i <PERSON> al<PERSON>", "@tafQurtubiD": {"type": "text", "placeholders": {}}, "tafQurtubiN": "<PERSON><PERSON><PERSON>", "@tafQurtubiN": {"type": "text", "placeholders": {}}, "tafSaadiD": "<PERSON><PERSON><PERSON>", "@tafSaadiD": {"type": "text", "placeholders": {}}, "tafSaadiN": "<PERSON><PERSON><PERSON>", "@tafSaadiN": {"type": "text", "placeholders": {}}, "tafTabariD": "<PERSON><PERSON> an <PERSON> [ay] al <PERSON>", "@tafTabariD": {"type": "text", "placeholders": {}}, "tafTabariN": "Tafsir <PERSON>", "@tafTabariN": {"type": "text", "placeholders": {}}, "tafseer": "Al <PERSON>", "@tafseer": {"type": "text", "placeholders": {}}, "tafsirLibrary": "Tafsir Library", "@tafsirLibrary": {"type": "text", "placeholders": {}}, "tenDaysOfDhul-Hijjah": "The First Ten Days of Dhul Hijjah", "@tenDaysOfDhul-Hijjah": {"type": "text", "placeholders": {}}, "themeTitle": "<PERSON>ose <PERSON>", "@themeTitle": {"type": "text", "placeholders": {}}, "to": "To", "@to": {"type": "text", "placeholders": {}}, "translation": "Translation", "@translation": {"type": "text", "placeholders": {}}, "validate_by_scholars": "Validate by Scholars", "version": "Version", "@version": {"type": "text", "placeholders": {}}, "waqfName": "Stop Signs", "@waqfName": {"type": "text", "placeholders": {}}, "welcome_back": "Welcome Back", "@welcome_back": {"type": "text", "placeholders": {}}, "year": "Year", "settings": "Settings", "@settings": {"type": "text", "placeholders": {}}, "reading_settings": "Reading Settings", "@reading_settings": {"type": "text", "placeholders": {}}, "font_size": "Font Size", "@font_size": {"type": "text", "placeholders": {}}, "medium": "Medium", "@medium": {"type": "text", "placeholders": {}}, "font_used": "Font Used", "@font_used": {"type": "text", "placeholders": {}}, "hafs_smart_font": "<PERSON>fs Smart Font", "@hafs_smart_font": {"type": "text", "placeholders": {}}, "show_translation": "Show Translation", "@show_translation": {"type": "text", "placeholders": {}}, "enabled": "Enabled", "@enabled": {"type": "text", "placeholders": {}}, "ai_settings": "AI Settings", "@ai_settings": {"type": "text", "placeholders": {}}, "automatic_analysis": "Automatic Analysis", "@automatic_analysis": {"type": "text", "placeholders": {}}, "detail_level": "Detail Level", "@detail_level": {"type": "text", "placeholders": {}}, "advanced": "Advanced", "@advanced": {"type": "text", "placeholders": {}}, "reset_preferences": "Reset Preferences", "@reset_preferences": {"type": "text", "placeholders": {}}, "restore_default_settings": "<PERSON><PERSON>", "@restore_default_settings": {"type": "text", "placeholders": {}}, "app_settings": "App Settings", "@app_settings": {"type": "text", "placeholders": {}}, "dark_mode": "Dark Mode", "@dark_mode": {"type": "text", "placeholders": {}}, "disabled": "Disabled", "@disabled": {"type": "text", "placeholders": {}}, "notifications": "Notifications", "@notifications": {"type": "text", "placeholders": {}}, "backup": "Backup", "@backup": {"type": "text", "placeholders": {}}, "automatic": "Automatic", "@automatic": {"type": "text", "placeholders": {}}, "app_info": "App Information", "@app_info": {"type": "text", "placeholders": {}}, "developer": "Developer", "@developer": {"type": "text", "placeholders": {}}, "quranic_insights_team": "Quranic Insights Team", "@quranic_insights_team": {"type": "text", "placeholders": {}}, "support": "Support", "@support": {"type": "text", "placeholders": {}}, "contact_us": "Contact Us", "@contact_us": {"type": "text", "placeholders": {}}, "admin_panel": "Admin Panel", "@admin_panel": {"type": "text", "placeholders": {}}, "manage_ai_settings": "Manage AI Settings", "@manage_ai_settings": {"type": "text", "placeholders": {}}, "control_models_prompts": "Control models and prompts", "@control_models_prompts": {"type": "text", "placeholders": {}}, "choose_font_size": "Choose the appropriate font size for reading:", "@choose_font_size": {"type": "text", "placeholders": {}}, "small": "Small", "@small": {"type": "text", "placeholders": {}}, "large": "Large", "@large": {"type": "text", "placeholders": {}}, "very_large": "Very Large", "@very_large": {"type": "text", "placeholders": {}}, "font_type": "Font Type", "@font_type": {"type": "text", "placeholders": {}}, "font_settings_development": "Font settings are under development", "@font_settings_development": {"type": "text", "placeholders": {}}, "translation_settings": "Translation Settings", "@translation_settings": {"type": "text", "placeholders": {}}, "translation_settings_development": "Translation settings are under development", "@translation_settings_development": {"type": "text", "placeholders": {}}, "ai_settings_development": "AI settings are under development", "@ai_settings_development": {"type": "text", "placeholders": {}}, "detail_level_settings_development": "Detail level settings are under development", "@detail_level_settings_development": {"type": "text", "placeholders": {}}, "confirm_reset_preferences": "Are you sure you want to restore default settings?", "@confirm_reset_preferences": {"type": "text", "placeholders": {}}, "confirm": "Confirm", "@confirm": {"type": "text", "placeholders": {}}, "preferences_reset": "Preferences have been reset", "@preferences_reset": {"type": "text", "placeholders": {}}, "appearance_settings": "Appearance Settings", "@appearance_settings": {"type": "text", "placeholders": {}}, "appearance_settings_development": "Appearance settings are under development", "@appearance_settings_development": {"type": "text", "placeholders": {}}, "notification_settings": "Notification Settings", "@notification_settings": {"type": "text", "placeholders": {}}, "notification_settings_development": "Notification settings are under development", "@notification_settings_development": {"type": "text", "placeholders": {}}, "backup_settings": "Backup Settings", "@backup_settings": {"type": "text", "placeholders": {}}, "backup_settings_development": "Backup settings are under development", "@backup_settings_development": {"type": "text", "placeholders": {}}, "quranic_insights": "Quranic Insights", "@quranic_insights": {"type": "text", "placeholders": {}}, "app_description": "An application for intelligent interaction with the Holy Quran using artificial intelligence technologies", "@app_description": {"type": "text", "placeholders": {}}, "developer_info": "Developer Information", "@developer_info": {"type": "text", "placeholders": {}}, "specializing_islamic_apps": "Specializing in Islamic application development", "@specializing_islamic_apps": {"type": "text", "placeholders": {}}, "support_message": "For support, please contact us via email or our website", "@support_message": {"type": "text", "placeholders": {}}, "@retry": {"type": "text", "placeholders": {}}, "maybe_later": "Maybe Later", "@maybe_later": {"type": "text", "placeholders": {}}, "all_local_data_will_sync": "All your local data will be automatically synced after signup", "@all_local_data_will_sync": {"type": "text", "placeholders": {}}, "join_to_save_progress": "Join to save your progress and sync across devices", "@join_to_save_progress": {"type": "text", "placeholders": {}}, "create_your_account": "Create Your Account", "@create_your_account": {"type": "text", "placeholders": {}}, "account_created_data_synced": "Account created! Your data is now synced to the cloud.", "@account_created_data_synced": {"type": "text", "placeholders": {}}, "please_enter_a_password": "Please enter a password", "@please_enter_a_password": {"type": "text", "placeholders": {}}, "i_agree_to_terms_and_privacy": "I agree to the Terms of Service and Privacy Policy", "@i_agree_to_terms_and_privacy": {"type": "text", "placeholders": {}}}