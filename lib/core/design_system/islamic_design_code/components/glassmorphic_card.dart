import 'dart:ui';
import 'package:flutter/material.dart';

/// Glassmorphic card component inspired by DesignCode UI
/// Adapted for Islamic content with cultural sensitivity
class GlassmorphicCard extends StatefulWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final Color? color;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  final VoidCallback? onTap;
  final bool showBorder;
  final bool enableHoverEffect;
  final List<BoxShadow>? shadows;

  const GlassmorphicCard({
    super.key,
    required this.child,
    this.blur = 10.0,
    this.opacity = 0.1,
    this.color,
    this.borderRadius,
    this.padding = const EdgeInsets.all(16),
    this.margin,
    this.width,
    this.height,
    this.onTap,
    this.showBorder = true,
    this.enableHoverEffect = true,
    this.shadows,
  });

  @override
  State<GlassmorphicCard> createState() => _GlassmorphicCardState();
}

class _GlassmorphicCardState extends State<GlassmorphicCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    if (!widget.enableHoverEffect) return;
    
    setState(() {
      _isHovered = isHovered;
    });
    
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final defaultColor = isDark 
        ? Colors.white.withOpacity(widget.opacity)
        : Colors.white.withOpacity(widget.opacity * 1.5);
    
    final borderColor = isDark
        ? Colors.white.withOpacity(0.2)
        : Colors.white.withOpacity(0.3);

    return AnimatedBuilder(
      animation: _hoverController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin,
            decoration: BoxDecoration(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
              boxShadow: [
                // Default shadow
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
                // Hover glow effect
                if (widget.enableHoverEffect)
                  BoxShadow(
                    color: theme.primaryColor.withOpacity(_glowAnimation.value * 0.3),
                    blurRadius: 30 * _glowAnimation.value,
                    offset: const Offset(0, 0),
                  ),
                // Custom shadows
                ...?widget.shadows,
              ],
            ),
            child: ClipRRect(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: widget.blur,
                  sigmaY: widget.blur,
                ),
                child: MouseRegion(
                  onEnter: (_) => _onHover(true),
                  onExit: (_) => _onHover(false),
                  child: GestureDetector(
                    onTap: widget.onTap,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            widget.color ?? defaultColor,
                            (widget.color ?? defaultColor).withOpacity(
                              widget.opacity * 0.5,
                            ),
                          ],
                        ),
                        borderRadius: widget.borderRadius ?? BorderRadius.circular(16),
                        border: widget.showBorder
                            ? Border.all(
                                color: borderColor,
                                width: 1,
                              )
                            : null,
                      ),
                      padding: widget.padding,
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Specialized glassmorphic card for Quranic verses
class QuranVerseCard extends StatelessWidget {
  final String arabicText;
  final String? translation;
  final String verseReference;
  final VoidCallback? onTap;
  final bool isBookmarked;
  final VoidCallback? onBookmarkTap;
  final VoidCallback? onShareTap;
  final VoidCallback? onInsightsTap;

  const QuranVerseCard({
    super.key,
    required this.arabicText,
    this.translation,
    required this.verseReference,
    this.onTap,
    this.isBookmarked = false,
    this.onBookmarkTap,
    this.onShareTap,
    this.onInsightsTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GlassmorphicCard(
      onTap: onTap,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Verse reference
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.primaryColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  verseReference,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: theme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Row(
                children: [
                  if (onInsightsTap != null)
                    _ActionButton(
                      icon: Icons.psychology,
                      onTap: onInsightsTap!,
                      tooltip: 'AI Insights',
                    ),
                  if (onBookmarkTap != null)
                    _ActionButton(
                      icon: isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                      onTap: onBookmarkTap!,
                      tooltip: 'Bookmark',
                      isActive: isBookmarked,
                    ),
                  if (onShareTap != null)
                    _ActionButton(
                      icon: Icons.share,
                      onTap: onShareTap!,
                      tooltip: 'Share',
                    ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Arabic text
          Text(
            arabicText,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontFamily: 'HafsSmart',
              fontSize: 24,
              height: 2.0,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.right,
            textDirection: TextDirection.rtl,
          ),
          
          if (translation != null) ...[
            const SizedBox(height: 12),
            Text(
              translation!,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.8),
                height: 1.6,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Small action button for verse cards
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  final String tooltip;
  final bool isActive;

  const _ActionButton({
    required this.icon,
    required this.onTap,
    required this.tooltip,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.only(left: 8),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isActive 
                ? theme.primaryColor.withOpacity(0.2)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: isActive 
                ? theme.primaryColor
                : theme.colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ),
    );
  }
}
