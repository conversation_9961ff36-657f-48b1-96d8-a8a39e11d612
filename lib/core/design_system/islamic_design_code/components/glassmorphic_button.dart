import 'dart:ui';
import 'package:flutter/material.dart';

/// Glassmorphic button component inspired by DesignCode UI
class GlassmorphicButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double blur;
  final double opacity;
  final Color? color;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  final double? width;
  final double? height;
  final bool showBorder;
  final bool enableGlowEffect;
  final IconData? icon;
  final String? text;
  final ButtonStyle? style;

  const GlassmorphicButton({
    super.key,
    required this.child,
    this.onPressed,
    this.blur = 10.0,
    this.opacity = 0.15,
    this.color,
    this.borderRadius,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    this.width,
    this.height,
    this.showBorder = true,
    this.enableGlowEffect = true,
    this.icon,
    this.text,
    this.style,
  });

  /// Primary button with enhanced styling
  factory GlassmorphicButton.primary({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    IconData? icon,
    double? width,
    double? height,
  }) {
    return GlassmorphicButton(
      key: key,
      onPressed: onPressed,
      width: width,
      height: height,
      opacity: 0.2,
      enableGlowEffect: true,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 20),
            const SizedBox(width: 8),
          ],
          Text(
            text,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Secondary button with subtle styling
  factory GlassmorphicButton.secondary({
    Key? key,
    required VoidCallback? onPressed,
    required String text,
    IconData? icon,
    double? width,
    double? height,
  }) {
    return GlassmorphicButton(
      key: key,
      onPressed: onPressed,
      width: width,
      height: height,
      opacity: 0.1,
      enableGlowEffect: false,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 18),
            const SizedBox(width: 8),
          ],
          Text(
            text,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Icon-only button
  factory GlassmorphicButton.icon({
    Key? key,
    required VoidCallback? onPressed,
    required IconData icon,
    double size = 48,
    String? tooltip,
  }) {
    return GlassmorphicButton(
      key: key,
      onPressed: onPressed,
      width: size,
      height: size,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(size / 2),
      child: Tooltip(
        message: tooltip ?? '',
        child: Icon(icon, size: size * 0.4),
      ),
    );
  }

  @override
  State<GlassmorphicButton> createState() => _GlassmorphicButtonState();
}

class _GlassmorphicButtonState extends State<GlassmorphicButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _controller.reverse();
    widget.onPressed?.call();
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isEnabled = widget.onPressed != null;
    
    final defaultColor = isDark 
        ? Colors.white.withOpacity(widget.opacity)
        : Colors.white.withOpacity(widget.opacity * 1.2);
    
    final borderColor = isDark
        ? Colors.white.withOpacity(0.3)
        : Colors.white.withOpacity(0.4);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height,
            decoration: BoxDecoration(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
              boxShadow: [
                // Default shadow
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
                // Glow effect when pressed
                if (widget.enableGlowEffect && _isPressed)
                  BoxShadow(
                    color: theme.primaryColor.withOpacity(_glowAnimation.value * 0.4),
                    blurRadius: 25 * _glowAnimation.value,
                    offset: const Offset(0, 0),
                  ),
              ],
            ),
            child: ClipRRect(
              borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: widget.blur,
                  sigmaY: widget.blur,
                ),
                child: GestureDetector(
                  onTapDown: isEnabled ? _onTapDown : null,
                  onTapUp: isEnabled ? _onTapUp : null,
                  onTapCancel: isEnabled ? _onTapCancel : null,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          widget.color ?? defaultColor,
                          (widget.color ?? defaultColor).withOpacity(
                            widget.opacity * 0.6,
                          ),
                        ],
                      ),
                      borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
                      border: widget.showBorder
                          ? Border.all(
                              color: borderColor,
                              width: 1,
                            )
                          : null,
                    ),
                    padding: widget.padding,
                    child: Center(
                      child: DefaultTextStyle(
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: isEnabled 
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.onSurface.withOpacity(0.5),
                        ) ?? const TextStyle(),
                        child: IconTheme(
                          data: IconThemeData(
                            color: isEnabled 
                                ? theme.colorScheme.onSurface
                                : theme.colorScheme.onSurface.withOpacity(0.5),
                          ),
                          child: widget.child,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Floating Action Button with glassmorphic design
class GlassmorphicFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final double size;

  const GlassmorphicFAB({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.size = 56,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GlassmorphicButton(
      onPressed: onPressed,
      width: size,
      height: size,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(size / 2),
      color: backgroundColor ?? theme.primaryColor.withOpacity(0.3),
      enableGlowEffect: true,
      child: Tooltip(
        message: tooltip ?? '',
        child: Icon(
          icon,
          size: size * 0.4,
          color: theme.colorScheme.onPrimary,
        ),
      ),
    );
  }
}

/// Toggle button with glassmorphic design
class GlassmorphicToggleButton extends StatelessWidget {
  final bool isSelected;
  final VoidCallback? onPressed;
  final Widget child;
  final double? width;
  final double? height;

  const GlassmorphicToggleButton({
    super.key,
    required this.isSelected,
    this.onPressed,
    required this.child,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GlassmorphicButton(
      onPressed: onPressed,
      width: width,
      height: height,
      color: isSelected 
          ? theme.primaryColor.withOpacity(0.3)
          : null,
      opacity: isSelected ? 0.3 : 0.1,
      enableGlowEffect: isSelected,
      child: DefaultTextStyle(
        style: TextStyle(
          color: isSelected 
              ? theme.primaryColor
              : theme.colorScheme.onSurface.withOpacity(0.7),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
        ),
        child: IconTheme(
          data: IconThemeData(
            color: isSelected 
                ? theme.primaryColor
                : theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          child: child,
        ),
      ),
    );
  }
}
