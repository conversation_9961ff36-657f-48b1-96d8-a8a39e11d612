/// Islamic DesignCode UI System
/// 
/// A revolutionary design system inspired by DesignCode UI,
/// adapted for Islamic applications with cultural sensitivity
/// and spiritual reverence.
/// 
/// This library provides:
/// - Glassmorphic components with Islamic aesthetics
/// - Prayer time-inspired gradient backgrounds
/// - Culturally appropriate animations and interactions
/// - Accessibility-first design principles

library islamic_design_code;

// Foundation
export 'foundation/gradient_backgrounds.dart';

// Components
export 'components/glassmorphic_card.dart';
export 'components/glassmorphic_button.dart';

// Demo
export 'demo/proof_of_concept_screen.dart';

/// Islamic DesignCode Theme Data
class IslamicDesignCodeTheme {
  static const String fontFamily = 'Inter';
  static const String arabicFontFamily = 'HafsSmart';
  
  // Spacing scale
  static const double spacing2xs = 2.0;
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 12.0;
  static const double spacingLg = 16.0;
  static const double spacingXl = 24.0;
  static const double spacing2xl = 32.0;
  static const double spacing3xl = 48.0;
  
  // Border radius scale
  static const double radiusXs = 4.0;
  static const double radiusSm = 8.0;
  static const double radiusMd = 12.0;
  static const double radiusLg = 16.0;
  static const double radiusXl = 24.0;
  static const double radius2xl = 32.0;
  
  // Blur values
  static const double blurSm = 5.0;
  static const double blurMd = 10.0;
  static const double blurLg = 15.0;
  static const double blurXl = 20.0;
  
  // Opacity values
  static const double opacityLight = 0.05;
  static const double opacityMedium = 0.1;
  static const double opacityStrong = 0.15;
  static const double opacityIntense = 0.2;
}

/// Extension methods for easier theme access
extension IslamicDesignCodeContext on BuildContext {
  /// Get the current theme
  ThemeData get theme => Theme.of(this);
  
  /// Check if dark mode is active
  bool get isDarkMode => theme.brightness == Brightness.dark;
  
  /// Get text theme
  TextTheme get textTheme => theme.textTheme;
  
  /// Get color scheme
  ColorScheme get colorScheme => theme.colorScheme;
  
  /// Get screen size
  Size get screenSize => MediaQuery.of(this).size;
  
  /// Check if screen is small (phone)
  bool get isSmallScreen => screenSize.width < 600;
  
  /// Check if screen is medium (tablet)
  bool get isMediumScreen => screenSize.width >= 600 && screenSize.width < 1200;
  
  /// Check if screen is large (desktop)
  bool get isLargeScreen => screenSize.width >= 1200;
}
