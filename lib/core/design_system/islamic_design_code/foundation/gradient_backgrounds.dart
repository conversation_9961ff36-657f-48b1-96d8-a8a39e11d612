import 'package:flutter/material.dart';

/// Islamic DesignCode-inspired gradient backgrounds
/// Inspired by prayer times and spiritual moments
class IslamicGradients {
  
  /// Fajr (Dawn) - Soft purples and pinks
  static const LinearGradient fajr = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF6B46C1), // Deep purple
      Color(0xFF9333EA), // Purple
      Color(0xFFEC4899), // Pink
      Color(0xFFF97316), // Orange
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
  );

  /// Dhuhr (Noon) - Bright blues and whites
  static const LinearGradient dhuhr = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF0EA5E9), // Sky blue
      Color(0xFF3B82F6), // Blue
      Color(0xFF6366F1), // Indigo
      Color(0xFF8B5CF6), // Violet
    ],
    stops: [0.0, 0.4, 0.7, 1.0],
  );

  /// Asr (Afternoon) - Golden and amber tones
  static const LinearGradient asr = LinearGradient(
    begin: Alignment.topRight,
    end: Alignment.bottomLeft,
    colors: [
      Color(0xFFFBBF24), // Yellow
      Color(0xFFF59E0B), // Amber
      Color(0xFFEA580C), // Orange
      Color(0xFFDC2626), // Red
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
  );

  /// Maghrib (Sunset) - Deep oranges and purples
  static const LinearGradient maghrib = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFF97316), // Orange
      Color(0xFFEA580C), // Dark orange
      Color(0xFFDC2626), // Red
      Color(0xFF7C2D12), // Dark red
    ],
    stops: [0.0, 0.3, 0.6, 1.0],
  );

  /// Isha (Night) - Deep blues and blacks
  static const LinearGradient isha = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF1E293B), // Dark slate
      Color(0xFF0F172A), // Darker slate
      Color(0xFF020617), // Almost black
      Color(0xFF000000), // Black
    ],
    stops: [0.0, 0.4, 0.8, 1.0],
  );

  /// Ramadan Special - Green and gold
  static const LinearGradient ramadan = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF059669), // Emerald
      Color(0xFF10B981), // Green
      Color(0xFFFBBF24), // Gold
      Color(0xFFF59E0B), // Amber
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
  );

  /// Hajj Special - Black and silver
  static const LinearGradient hajj = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF374151), // Gray
      Color(0xFF1F2937), // Dark gray
      Color(0xFF111827), // Darker gray
      Color(0xFF000000), // Black
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
  );

  /// Get gradient based on current prayer time
  static LinearGradient getCurrentPrayerGradient() {
    final now = DateTime.now();
    final hour = now.hour;

    // Simplified prayer time logic (would be replaced with actual prayer times)
    if (hour >= 5 && hour < 7) return fajr;      // Dawn
    if (hour >= 7 && hour < 12) return dhuhr;    // Morning to noon
    if (hour >= 12 && hour < 15) return dhuhr;   // Noon
    if (hour >= 15 && hour < 18) return asr;     // Afternoon
    if (hour >= 18 && hour < 20) return maghrib; // Sunset
    return isha; // Night
  }

  /// Get all available gradients
  static List<LinearGradient> getAllGradients() {
    return [fajr, dhuhr, asr, maghrib, isha, ramadan, hajj];
  }

  /// Get gradient names
  static List<String> getGradientNames() {
    return ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha', 'Ramadan', 'Hajj'];
  }
}

/// Animated gradient background widget
class IslamicGradientBackground extends StatefulWidget {
  final Widget child;
  final LinearGradient? gradient;
  final Duration animationDuration;
  final bool autoUpdateWithPrayerTime;

  const IslamicGradientBackground({
    super.key,
    required this.child,
    this.gradient,
    this.animationDuration = const Duration(seconds: 2),
    this.autoUpdateWithPrayerTime = true,
  });

  @override
  State<IslamicGradientBackground> createState() => _IslamicGradientBackgroundState();
}

class _IslamicGradientBackgroundState extends State<IslamicGradientBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  LinearGradient? _currentGradient;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    
    _currentGradient = widget.gradient ?? IslamicGradients.getCurrentPrayerGradient();
    _controller.forward();

    // Auto-update with prayer time if enabled
    if (widget.autoUpdateWithPrayerTime) {
      _startPrayerTimeUpdates();
    }
  }

  void _startPrayerTimeUpdates() {
    // Update every hour to check for prayer time changes
    Stream.periodic(const Duration(hours: 1)).listen((_) {
      final newGradient = IslamicGradients.getCurrentPrayerGradient();
      if (newGradient != _currentGradient) {
        setState(() {
          _currentGradient = newGradient;
        });
        _controller.reset();
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: _currentGradient,
          ),
          child: widget.child,
        );
      },
    );
  }
}
