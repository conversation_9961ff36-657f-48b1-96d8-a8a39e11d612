import 'package:flutter/material.dart';
import '../foundation/gradient_backgrounds.dart';
import '../components/glassmorphic_card.dart';
import '../components/glassmorphic_button.dart';

/// Proof of Concept screen demonstrating Islamic DesignCode UI
class IslamicDesignCodeProofOfConcept extends StatefulWidget {
  const IslamicDesignCodeProofOfConcept({super.key});

  @override
  State<IslamicDesignCodeProofOfConcept> createState() =>
      _IslamicDesignCodeProofOfConceptState();
}

class _IslamicDesignCodeProofOfConceptState
    extends State<IslamicDesignCodeProofOfConcept> {
  int _selectedGradientIndex = 0;
  bool _isDarkMode = false;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? ThemeData.dark() : ThemeData.light(),
      child: Scaffold(
        body: IslamicGradientBackground(
          gradient: IslamicGradients.getAllGradients()[_selectedGradientIndex],
          autoUpdateWithPrayerTime: false,
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Theme selector
                        _buildThemeSelector(),

                        const SizedBox(height: 24),

                        // Sample Quran verses
                        _buildQuranVerses(),

                        const SizedBox(height: 24),

                        // AI Insights demo
                        _buildAIInsightsDemo(),

                        const SizedBox(height: 24),

                        // Action buttons
                        _buildActionButtons(),

                        const SizedBox(height: 24),

                        // Stats cards
                        _buildStatsCards(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: GlassmorphicFAB(
          icon: Icons.psychology,
          tooltip: 'AI Insights',
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('AI Insights feature coming soon!')),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return GlassmorphicCard(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Islamic DesignCode',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Proof of Concept',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.color
                            ?.withOpacity(0.7),
                      ),
                ),
              ],
            ),
          ),
          GlassmorphicButton.icon(
            icon: _isDarkMode ? Icons.light_mode : Icons.dark_mode,
            onPressed: () {
              setState(() {
                _isDarkMode = !_isDarkMode;
              });
            },
            tooltip: 'Toggle theme',
          ),
        ],
      ),
    );
  }

  Widget _buildThemeSelector() {
    return GlassmorphicCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Prayer Time Themes',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: IslamicGradients.getGradientNames()
                .asMap()
                .entries
                .map((entry) {
              final index = entry.key;
              final name = entry.value;
              return GlassmorphicToggleButton(
                isSelected: _selectedGradientIndex == index,
                onPressed: () {
                  setState(() {
                    _selectedGradientIndex = index;
                  });
                },
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(name),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildQuranVerses() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            'Quranic Verses',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
          ),
        ),
        QuranVerseCard(
          arabicText: 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
          translation:
              'In the name of Allah, the Entirely Merciful, the Especially Merciful.',
          verseReference: 'Al-Fatiha 1:1',
          isBookmarked: true,
          onBookmarkTap: () {},
          onShareTap: () {},
          onInsightsTap: () {},
        ),
        QuranVerseCard(
          arabicText: 'الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ',
          translation: '[All] praise is [due] to Allah, Lord of the worlds.',
          verseReference: 'Al-Fatiha 1:2',
          onBookmarkTap: () {},
          onShareTap: () {},
          onInsightsTap: () {},
        ),
      ],
    );
  }

  Widget _buildAIInsightsDemo() {
    return GlassmorphicCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.psychology,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'AI Insights',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'This verse emphasizes the importance of seeking Allah\'s guidance and mercy. The phrase "Bismillah" is recited before beginning any significant action in Islamic tradition.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.6,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: GlassmorphicButton.secondary(
                  text: 'Related Verses',
                  icon: Icons.link,
                  onPressed: () {},
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GlassmorphicButton.secondary(
                  text: 'Scholar Notes',
                  icon: Icons.school,
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: GlassmorphicButton.primary(
            text: 'Continue Reading',
            icon: Icons.menu_book,
            onPressed: () {},
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: GlassmorphicButton.secondary(
            text: 'Prayer Times',
            icon: Icons.access_time,
            onPressed: () {},
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Verses Read',
            value: '127',
            icon: Icons.menu_book,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'AI Insights',
            value: '23',
            icon: Icons.psychology,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Bookmarks',
            value: '45',
            icon: Icons.bookmark,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
  }) {
    return GlassmorphicCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(
            icon,
            size: 32,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context)
                      .textTheme
                      .bodySmall
                      ?.color
                      ?.withOpacity(0.7),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
