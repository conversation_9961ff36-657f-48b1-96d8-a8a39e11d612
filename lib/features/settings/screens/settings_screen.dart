import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb; // For platform detection
import 'dart:io' show Platform; // For platform detection
import 'package:go_router/go_router.dart'; // For navigation
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:get/get.dart'; // For localization
import '/core/config/app_config.dart';
import '/core/navigation/app_router.dart'; // For admin route

class SettingsScreen extends HookConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Determine if the admin section should be shown
    bool showAdminSection = false;
    if (kIsWeb) {
      showAdminSection = true;
    } else {
      try {
        if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
          showAdminSection = true;
        }
      } catch (e) {
        // Platform detection might fail in some test environments or unsupported platforms
        // Log error or handle gracefully if needed
        print('Error detecting platform: $e');
      }
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            'settings'.tr,
            style: const TextStyle(
              fontSize: AppConfig.headingFontSize,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildSection(
                title: 'reading_settings'.tr,
                icon: Icons.menu_book,
                color: Colors.green,
                items: <Widget>[
                  _buildSettingItem(
                    title: 'font_size'.tr,
                    subtitle: 'medium'.tr,
                    icon: Icons.text_fields,
                    onTap: () => _showFontSizeSettings(context),
                  ),
                  _buildSettingItem(
                    title: 'font_used'.tr,
                    subtitle: 'hafs_smart_font'.tr,
                    icon: Icons.font_download,
                    onTap: () => _showFontSettings(context),
                  ),
                  _buildSettingItem(
                    title: 'show_translation'.tr,
                    subtitle: 'enabled'.tr,
                    icon: Icons.translate,
                    onTap: () => _showTranslationSettings(context),
                  ),
                ],
              ),
              const SizedBox(height: AppConfig.largePadding),
              _buildSection(
                title: 'ai_settings'.tr,
                icon: Icons.psychology,
                color: Colors.purple,
                items: <Widget>[
                  _buildSettingItem(
                    title: 'automatic_analysis'.tr,
                    subtitle: 'enabled'.tr,
                    icon: Icons.auto_fix_high,
                    onTap: () => _showAISettings(context),
                  ),
                  _buildSettingItem(
                    title: 'detail_level'.tr,
                    subtitle: 'advanced'.tr,
                    icon: Icons.tune,
                    onTap: () => _showDetailLevelSettings(context),
                  ),
                  _buildSettingItem(
                    title: 'reset_preferences'.tr,
                    subtitle: 'restore_default_settings'.tr,
                    icon: Icons.refresh,
                    onTap: () => _showResetDialog(context),
                  ),
                ],
              ),
              const SizedBox(height: AppConfig.largePadding),
              _buildSection(
                title: 'app_settings'.tr,
                icon: Icons.settings,
                color: Colors.blue,
                items: <Widget>[
                  _buildSettingItem(
                    title: 'dark_mode'.tr,
                    subtitle: 'disabled'.tr,
                    icon: Icons.dark_mode,
                    onTap: () => _showThemeSettings(context),
                  ),
                  _buildSettingItem(
                    title: 'notifications'.tr,
                    subtitle: 'enabled'.tr,
                    icon: Icons.notifications,
                    onTap: () => _showNotificationSettings(context),
                  ),
                  _buildSettingItem(
                    title: 'backup'.tr,
                    subtitle: 'automatic'.tr,
                    icon: Icons.backup,
                    onTap: () => _showBackupSettings(context),
                  ),
                ],
              ),
              const SizedBox(height: AppConfig.largePadding),
              _buildSection(
                title: 'app_info'.tr,
                icon: Icons.info,
                color: Colors.orange,
                items: <Widget>[
                  _buildSettingItem(
                    title: 'version'.tr,
                    subtitle: '1.0.0',
                    icon: Icons.info_outline,
                    onTap: () => _showAboutDialog(context),
                  ),
                  _buildSettingItem(
                    title: 'developer'.tr,
                    subtitle: 'quranic_insights_team'.tr,
                    icon: Icons.code,
                    onTap: () => _showDeveloperInfo(context),
                  ),
                  _buildSettingItem(
                    title: 'support'.tr,
                    subtitle: 'contact_us'.tr,
                    icon: Icons.support,
                    onTap: () => _showSupportInfo(context),
                  ),
                ],
              ),
              if (showAdminSection) ...<Widget>[
                const SizedBox(height: AppConfig.largePadding),
                _buildSection(
                  title: 'admin_panel'.tr,
                  icon: Icons.admin_panel_settings,
                  color: Colors.redAccent,
                  items: <Widget>[
                    _buildSettingItem(
                      title: 'manage_ai_settings'.tr,
                      subtitle: 'control_models_prompts'.tr,
                      icon: Icons.model_training,
                      onTap: () => context.goNamed(AppRouter.adminRouteName), // Corrected to use route name
                    ),
                  ],
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> items,
  }) => Card(
      elevation: AppConfig.cardElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                Icon(icon, color: color, size: 28),
                const SizedBox(width: AppConfig.smallPadding),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            ...items,
          ],
        ),
      ),
    );

  Widget _buildSettingItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) => Column(
      children: <Widget>[
        ListTile(
          leading: Icon(icon, color: Colors.grey.shade600),
          title: Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: Text(
            subtitle,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: onTap,
        ),
        const Divider(height: 1),
      ],
    );

  void _showFontSizeSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('font_size'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text('choose_font_size'.tr),
            const SizedBox(height: 16),
            ...['small'.tr, 'medium'.tr, 'large'.tr, 'very_large'.tr].map((String size) =>
              RadioListTile<String>(
                title: Text(size),
                value: size,
                groupValue: 'medium'.tr,
                onChanged: (String? value) {
                  // TODO: Implement font size change
                },
              ),
            ),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('save'.tr),
          ),
        ],
      ),
    );
  }

  void _showFontSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('font_type'.tr),
        content: Text('font_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showTranslationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('translation_settings'.tr),
        content: Text('translation_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showAISettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('ai_settings'.tr),
        content: Text('ai_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showDetailLevelSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('detail_level'.tr),
        content: Text('detail_level_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('reset_preferences'.tr),
        content: Text('confirm_reset_preferences'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('preferences_reset'.tr)),
              );
            },
            child: Text('confirm'.tr),
          ),
        ],
      ),
    );
  }

  void _showThemeSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('appearance_settings'.tr),
        content: Text('appearance_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('notification_settings'.tr),
        content: Text('notification_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showBackupSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('backup_settings'.tr),
        content: Text('backup_settings_development'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'quranic_insights'.tr,
      applicationVersion: '1.0.0',
      applicationLegalese: '© 2024 ${'quranic_insights_team'.tr}',
      children: <Widget>[
        Text(
          'app_description'.tr,
        ),
      ],
    );
  }

  void _showDeveloperInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('developer_info'.tr),
        content: Text('${'quranic_insights_team'.tr}\n${'specializing_islamic_apps'.tr}'),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }

  void _showSupportInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text('support'.tr),
        content: Text('support_message'.tr),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('close'.tr),
          ),
        ],
      ),
    );
  }
}
